{% extends 'base.html' %}

{% block title %}لوحة تحكم التقارير - نظام إدارة مخازن المحروقات{% endblock %}

{% block content %}
<!-- Header Section -->
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-3 mb-4">
    <div>
        <h1 class="h2 mb-2" style="background: linear-gradient(135deg, #8145b2 0%, #6366f1 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 700;">
            <i class="fas fa-chart-line me-2"></i>لوحة تحكم التقارير المتقدمة
        </h1>
        <p class="text-muted mb-0">تقارير شاملة وتحليلات متقدمة لنظام إدارة المخازن</p>
    </div>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2" role="group">
            <button type="button" class="btn btn-outline-primary" onclick="refreshReports()">
                <i class="fas fa-sync-alt me-2"></i>تحديث التقارير
            </button>
            <button type="button" class="btn btn-outline-success" onclick="scheduleReport()">
                <i class="fas fa-clock me-2"></i>جدولة تقرير
            </button>
        </div>
        <div class="dropdown">
            <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <i class="fas fa-download me-2"></i>تصدير شامل
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#" onclick="exportAllReports('pdf')"><i class="fas fa-file-pdf me-2"></i>تصدير PDF</a></li>
                <li><a class="dropdown-item" href="#" onclick="exportAllReports('excel')"><i class="fas fa-file-excel me-2"></i>تصدير Excel</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="#" onclick="emailReports()"><i class="fas fa-envelope me-2"></i>إرسال بالبريد</a></li>
            </ul>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="row mb-5">
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card stat-card border-right-primary h-100">
            <div class="card-body position-relative">
                <div class="row no-gutters align-items-center">
                    <div class="col">
                        <div class="stat-label mb-2">إجمالي التقارير</div>
                        <div class="stat-number">{{ total_reports|default:12 }}</div>
                        <div class="mt-2">
                            <small class="text-primary">
                                <i class="fas fa-chart-line me-1"></i>متاح الآن
                            </small>
                        </div>
                    </div>
                </div>
                <i class="fas fa-file-alt fa-2x"></i>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card stat-card border-right-success h-100">
            <div class="card-body position-relative">
                <div class="row no-gutters align-items-center">
                    <div class="col">
                        <div class="stat-label mb-2">تقارير مجدولة</div>
                        <div class="stat-number">{{ scheduled_reports|default:5 }}</div>
                        <div class="mt-2">
                            <small class="text-success">
                                <i class="fas fa-clock me-1"></i>نشطة
                            </small>
                        </div>
                    </div>
                </div>
                <i class="fas fa-calendar-check fa-2x"></i>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card stat-card border-right-info h-100">
            <div class="card-body position-relative">
                <div class="row no-gutters align-items-center">
                    <div class="col">
                        <div class="stat-label mb-2">تقارير اليوم</div>
                        <div class="stat-number">{{ today_reports|default:8 }}</div>
                        <div class="mt-2">
                            <small class="text-info">
                                <i class="fas fa-arrow-up me-1"></i>+3 من أمس
                            </small>
                        </div>
                    </div>
                </div>
                <i class="fas fa-chart-bar fa-2x"></i>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
        <div class="card stat-card border-right-warning h-100">
            <div class="card-body position-relative">
                <div class="row no-gutters align-items-center">
                    <div class="col">
                        <div class="stat-label mb-2">تنبيهات التقارير</div>
                        <div class="stat-number">{{ report_alerts|default:2 }}</div>
                        <div class="mt-2">
                            <small class="text-warning">
                                <i class="fas fa-exclamation-triangle me-1"></i>تحتاج مراجعة
                            </small>
                        </div>
                    </div>
                </div>
                <i class="fas fa-bell fa-2x"></i>
            </div>
        </div>
    </div>
</div>

<!-- Reports Grid -->
<div class="row">
    <!-- تقرير حركة عامة للمخزن -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 stat-card report-card" data-report="storage-movement">
            <div class="card-body position-relative">
                <div class="report-icon mb-3">
                    <div class="icon-wrapper">
                        <i class="fas fa-warehouse fa-lg"></i>
                    </div>
                </div>
                <h5 class="card-title mb-3">تقرير حركة عامة للمخزن</h5>
                <p class="card-text text-muted mb-4">عرض جميع العمليات للمخزن المحدد (وارد، صادر، مرتجعات، تلف)</p>

                <div class="report-stats mb-3">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="stat-mini">
                                <div class="stat-mini-number">{{ storage_operations_count|default:156 }}</div>
                                <div class="stat-mini-label">عملية</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-mini">
                                <div class="stat-mini-number">{{ active_storages|default:8 }}</div>
                                <div class="stat-mini-label">مخزن نشط</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer d-flex gap-2">
                <a href="{% url 'fuel_storage:storage_movement_report' %}" class="btn btn-primary flex-fill">
                    <i class="fas fa-chart-line me-1"></i>عرض التقرير
                </a>
                <button type="button" class="btn btn-outline-primary" onclick="previewReport('storage-movement')">
                    <i class="fas fa-eye"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- تقرير حالة مخزن -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 stat-card report-card" data-report="storage-status">
            <div class="card-body position-relative">
                <div class="report-icon mb-3">
                    <div class="icon-wrapper success">
                        <i class="fas fa-boxes fa-lg"></i>
                    </div>
                </div>
                <h5 class="card-title mb-3">تقرير حالة مخزن</h5>
                <p class="card-text text-muted mb-4">عرض أصناف المخزن مع الكميات المتوفرة والحالة الحالية</p>

                <div class="report-stats mb-3">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="stat-mini">
                                <div class="stat-mini-number">{{ total_items|default:45 }}</div>
                                <div class="stat-mini-label">صنف</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-mini">
                                <div class="stat-mini-number">{{ low_stock_count|default:3 }}</div>
                                <div class="stat-mini-label">منخفض</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer d-flex gap-2">
                <a href="{% url 'fuel_storage:storage_status_report' %}" class="btn btn-success flex-fill">
                    <i class="fas fa-clipboard-list me-1"></i>عرض التقرير
                </a>
                <button type="button" class="btn btn-outline-success" onclick="previewReport('storage-status')">
                    <i class="fas fa-eye"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- تقرير العمليات الشهرية -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 stat-card report-card" data-report="monthly-operations">
            <div class="card-body position-relative">
                <div class="report-icon mb-3">
                    <div class="icon-wrapper info">
                        <i class="fas fa-chart-bar fa-lg"></i>
                    </div>
                </div>
                <h5 class="card-title mb-3">تقرير العمليات الشهرية</h5>
                <p class="card-text text-muted mb-4">إحصائيات شاملة للعمليات خلال الشهر الحالي</p>

                <div class="report-stats mb-3">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="stat-mini">
                                <div class="stat-mini-number">{{ monthly_operations|default:89 }}</div>
                                <div class="stat-mini-label">عملية</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-mini">
                                <div class="stat-mini-number">{{ growth_rate|default:"+12%" }}</div>
                                <div class="stat-mini-label">نمو</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer d-flex gap-2">
                <button type="button" class="btn btn-info flex-fill" onclick="generateMonthlyReport()">
                    <i class="fas fa-chart-pie me-1"></i>إنشاء التقرير
                </button>
                <button type="button" class="btn btn-outline-info" onclick="previewReport('monthly-operations')">
                    <i class="fas fa-eye"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- تقرير المرتجعات -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 stat-card report-card" data-report="returns">
            <div class="card-body position-relative">
                <div class="report-icon mb-3">
                    <div class="icon-wrapper warning">
                        <i class="fas fa-undo fa-lg"></i>
                    </div>
                </div>
                <h5 class="card-title mb-3">تقرير المرتجعات</h5>
                <p class="card-text text-muted mb-4">تحليل شامل لعمليات الإرجاع والمرتجعات</p>

                <div class="report-stats mb-3">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="stat-mini">
                                <div class="stat-mini-number">{{ total_returns|default:12 }}</div>
                                <div class="stat-mini-label">مرتجع</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-mini">
                                <div class="stat-mini-number">{{ return_value|default:"2.5K" }}</div>
                                <div class="stat-mini-label">قيمة</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer d-flex gap-2">
                <a href="{% url 'fuel_storage:returns_summary' %}" class="btn btn-warning flex-fill">
                    <i class="fas fa-list me-1"></i>عرض التقرير
                </a>
                <button type="button" class="btn btn-outline-warning" onclick="previewReport('returns')">
                    <i class="fas fa-eye"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- تقرير التلف -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 stat-card report-card" data-report="damage">
            <div class="card-body position-relative">
                <div class="report-icon mb-3">
                    <div class="icon-wrapper danger">
                        <i class="fas fa-exclamation-triangle fa-lg"></i>
                    </div>
                </div>
                <h5 class="card-title mb-3">تقرير التلف</h5>
                <p class="card-text text-muted mb-4">تتبع وتحليل عمليات التلف والخسائر</p>

                <div class="report-stats mb-3">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="stat-mini">
                                <div class="stat-mini-number">{{ damage_operations|default:5 }}</div>
                                <div class="stat-mini-label">عملية تلف</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-mini">
                                <div class="stat-mini-number">{{ damage_percentage|default:"0.8%" }}</div>
                                <div class="stat-mini-label">نسبة</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer d-flex gap-2">
                <a href="{% url 'fuel_storage:damage_operations_list' %}" class="btn btn-danger flex-fill">
                    <i class="fas fa-exclamation-circle me-1"></i>عرض التقرير
                </a>
                <button type="button" class="btn btn-outline-danger" onclick="previewReport('damage')">
                    <i class="fas fa-eye"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- تقرير مخصص -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 stat-card report-card" data-report="custom">
            <div class="card-body position-relative">
                <div class="report-icon mb-3">
                    <div class="icon-wrapper" style="background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);">
                        <i class="fas fa-cogs fa-lg"></i>
                    </div>
                </div>
                <h5 class="card-title mb-3">تقرير مخصص</h5>
                <p class="card-text text-muted mb-4">إنشاء تقارير مخصصة حسب احتياجاتك</p>

                <div class="report-stats mb-3">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="stat-mini">
                                <div class="stat-mini-number">{{ custom_reports|default:3 }}</div>
                                <div class="stat-mini-label">محفوظ</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-mini">
                                <div class="stat-mini-number">∞</div>
                                <div class="stat-mini-label">إمكانيات</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer d-flex gap-2">
                <button type="button" class="btn flex-fill" style="background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%); color: white;" onclick="openCustomReportBuilder()">
                    <i class="fas fa-plus me-1"></i>إنشاء تقرير
                </button>
                <button type="button" class="btn btn-outline-secondary" onclick="loadSavedReports()">
                    <i class="fas fa-folder"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- معلومات إضافية -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">ميزات التقارير</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6><i class="fas fa-calendar-alt text-primary me-2"></i>فلترة بالتاريخ</h6>
                        <p class="text-muted">جميع التقارير تدعم الفلترة بنطاق تاريخي محدد</p>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="fas fa-file-pdf text-danger me-2"></i>تصدير PDF</h6>
                        <p class="text-muted">إمكانية تصدير جميع التقارير بصيغة PDF</p>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="fas fa-file-excel text-success me-2"></i>تصدير Excel</h6>
                        <p class="text-muted">إمكانية تصدير جميع التقارير بصيغة Excel</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Custom CSS for Reports -->
<style>
.report-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.report-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.report-icon {
    text-align: center;
}

.report-stats .stat-mini {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 0.5rem;
    padding: 0.75rem;
}

.report-stats .stat-mini-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2563eb;
}

.report-stats .stat-mini-label {
    font-size: 0.75rem;
    color: #64748b;
    font-weight: 500;
}
</style>

<!-- JavaScript for Reports Dashboard -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add click animation to report cards
    const reportCards = document.querySelectorAll('.report-card');
    reportCards.forEach(card => {
        card.addEventListener('click', function(e) {
            if (!e.target.closest('.btn')) {
                const reportType = this.dataset.report;
                showReportPreview(reportType);
            }
        });
    });
});

// Refresh reports function
function refreshReports() {
    showLoadingOverlay('جاري تحديث التقارير...');

    // Simulate API call
    setTimeout(() => {
        hideLoadingOverlay();
        showToast('تم التحديث', 'تم تحديث جميع التقارير بنجاح', 'success');

        // Update stats with animation
        animateCounters();
    }, 2000);
}

// Schedule report function
function scheduleReport() {
    const modalHTML = `
        <div class="modal fade" id="scheduleModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-clock me-2"></i>جدولة تقرير
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="scheduleForm">
                            <div class="mb-3">
                                <label class="form-label">نوع التقرير</label>
                                <select class="form-select" required>
                                    <option value="">اختر نوع التقرير</option>
                                    <option value="storage-movement">تقرير حركة المخزن</option>
                                    <option value="storage-status">تقرير حالة المخزن</option>
                                    <option value="monthly-operations">تقرير العمليات الشهرية</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">التكرار</label>
                                <select class="form-select" required>
                                    <option value="daily">يومي</option>
                                    <option value="weekly">أسبوعي</option>
                                    <option value="monthly">شهري</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">وقت الإرسال</label>
                                <input type="time" class="form-control" value="09:00" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" placeholder="<EMAIL>" required>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-primary" onclick="saveSchedule()">
                            <i class="fas fa-save me-1"></i>حفظ الجدولة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHTML);
    const modal = new bootstrap.Modal(document.getElementById('scheduleModal'));
    modal.show();

    document.getElementById('scheduleModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

// Save schedule function
function saveSchedule() {
    const form = document.getElementById('scheduleForm');
    if (form.checkValidity()) {
        const modal = bootstrap.Modal.getInstance(document.getElementById('scheduleModal'));
        modal.hide();

        showToast('تم الحفظ', 'تم جدولة التقرير بنجاح', 'success');
    } else {
        form.reportValidity();
    }
}

// Export all reports function
function exportAllReports(format) {
    showLoadingOverlay(`جاري تصدير التقارير بصيغة ${format.toUpperCase()}...`);

    setTimeout(() => {
        hideLoadingOverlay();
        showToast('تم التصدير', `تم تصدير جميع التقارير بصيغة ${format.toUpperCase()} بنجاح`, 'success');
    }, 3000);
}

// Email reports function
function emailReports() {
    showConfirmDialog(
        'إرسال التقارير',
        'هل تريد إرسال جميع التقارير إلى البريد الإلكتروني المسجل؟',
        function() {
            showLoadingOverlay('جاري إرسال التقارير...');
            setTimeout(() => {
                hideLoadingOverlay();
                showToast('تم الإرسال', 'تم إرسال التقارير بنجاح', 'success');
            }, 2000);
        }
    );
}

// Preview report function
function previewReport(reportType) {
    showLoadingOverlay('جاري تحضير معاينة التقرير...');

    setTimeout(() => {
        hideLoadingOverlay();
        showToast('معاينة التقرير', `تم تحضير معاينة تقرير ${getReportName(reportType)}`, 'info');
    }, 1500);
}

// Generate monthly report
function generateMonthlyReport() {
    showLoadingOverlay('جاري إنشاء التقرير الشهري...');

    setTimeout(() => {
        hideLoadingOverlay();
        showToast('تم الإنشاء', 'تم إنشاء التقرير الشهري بنجاح', 'success');
    }, 2500);
}

// Open custom report builder
function openCustomReportBuilder() {
    showToast('قريباً', 'منشئ التقارير المخصصة سيكون متاحاً قريباً', 'info');
}

// Load saved reports
function loadSavedReports() {
    showToast('التقارير المحفوظة', 'عرض التقارير المحفوظة', 'info');
}

// Helper function to get report name
function getReportName(reportType) {
    const names = {
        'storage-movement': 'حركة المخزن',
        'storage-status': 'حالة المخزن',
        'monthly-operations': 'العمليات الشهرية',
        'returns': 'المرتجعات',
        'damage': 'التلف',
        'custom': 'مخصص'
    };
    return names[reportType] || reportType;
}

// Animate counters
function animateCounters() {
    const counters = document.querySelectorAll('.stat-mini-number');
    counters.forEach(counter => {
        const target = parseInt(counter.textContent);
        if (!isNaN(target)) {
            let current = 0;
            const increment = target / 20;
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    counter.textContent = target;
                    clearInterval(timer);
                } else {
                    counter.textContent = Math.floor(current);
                }
            }, 50);
        }
    });
}

// Show report preview
function showReportPreview(reportType) {
    const reportName = getReportName(reportType);
    showToast('معاينة التقرير', `انقر على "عرض التقرير" لفتح ${reportName}`, 'info');
}
</script>

{% endblock %}
