# استيراد مكتبة إدارة لوحة تحكم Django
from django.contrib import admin

# استيراد لوحة تحكم المستخدمين المدمجة في Django لإدارة المستخدمين المخصصين
from django.contrib.auth.admin import UserAdmin

# استيراد جميع الموديلات (Models) التي سيتم عرضها وإدارتها في لوحة التحكم
from .models import (
    CustomUser, Category, Station, Supplier, Beneficiary, 
    Storage, StorageItem, IncomingOperation, IncomingOperationItem, 
    IncomingOperationFile, OutgoingOperation, OutgoingOperationItem,
    OutgoingOperationFile, OperationModification, IncomingReturn,
    IncomingReturnItem, IncomingReturnFile, OutgoingReturn,
    OutgoingReturnItem, OutgoingReturnFile, DamageOperation,
    DamageOperationItem, DamageOperationFile, StorageTransfer,
    StorageTransferItem, StorageTransferFile
)

# ================= إدارة المستخدمين =================
@admin.register(CustomUser)  # تسجيل موديل CustomUser في لوحة التحكم
class CustomUserAdmin(UserAdmin):
    # الأعمدة التي ستظهر في قائمة المستخدمين
    list_display = ('username', 'full_name', 'user_type', 'is_active')
    # خيارات الفلترة الجانبية
    list_filter = ('user_type', 'is_active')
    # البحث في الحقول التالية
    search_fields = ('username', 'full_name')
    
    # إضافة حقول جديدة إلى صفحة تعديل المستخدم
    fieldsets = UserAdmin.fieldsets + (
        ('معلومات إضافية', {'fields': ('full_name', 'user_type')}),
    )

# ================= إدارة التصنيفات =================
@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name',)
    list_per_page = 20  # عدد العناصر في الصفحة الواحدة

# ================= إدارة المحطات =================
@admin.register(Station)
class StationAdmin(admin.ModelAdmin):
    list_display = ('name', 'address', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name', 'address')
    list_per_page = 20

# ================= إدارة الموردين =================
@admin.register(Supplier)
class SupplierAdmin(admin.ModelAdmin):
    list_display = ('full_name', 'phone_number', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('full_name', 'phone_number')
    list_per_page = 20

# ================= إدارة المستفيدين =================
@admin.register(Beneficiary)
class BeneficiaryAdmin(admin.ModelAdmin):
    list_display = ('full_name', 'phone_number', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('full_name', 'phone_number')
    list_per_page = 20

# ================= إدارة المخازن =================
@admin.register(Storage)
class StorageAdmin(admin.ModelAdmin):
    list_display = ('name', 'classification', 'keeper_name', 'phone_number', 'is_active', 'created_at')
    list_filter = ('classification', 'is_active', 'created_at')
    search_fields = ('name', 'keeper_name')
    list_per_page = 20

# ================= إدارة عناصر المخازن =================
@admin.register(StorageItem)
class StorageItemAdmin(admin.ModelAdmin):
    list_display = ('storage', 'category', 'unit_of_measure', 'opening_balance', 'current_quantity', 'updated_at')
    list_filter = ('storage', 'category', 'unit_of_measure')
    search_fields = ('storage__name', 'category__name')
    list_per_page = 20

# ================= مكونات فرعية لعمليات الوارد =================
class IncomingOperationItemInline(admin.TabularInline):
    model = IncomingOperationItem  # ربط بعناصر عملية الوارد
    extra = 1  # عدد الصفوف الفارغة الافتراضية

class IncomingOperationFileInline(admin.TabularInline):
    model = IncomingOperationFile  # ربط بالملفات المرفقة
    extra = 1

# ================= إدارة عمليات الوارد =================
@admin.register(IncomingOperation)
class IncomingOperationAdmin(admin.ModelAdmin):
    list_display = ('paper_number', 'storage', 'supplier', 'operation_date', 'created_by', 'created_at')
    list_filter = ('storage', 'supplier', 'station', 'operation_date')
    search_fields = ('paper_number', 'supply_document_number', 'deliverer_name', 'receiver_name')
    date_hierarchy = 'operation_date'
    inlines = [IncomingOperationItemInline, IncomingOperationFileInline]  # عرض العناصر والملفات مع العملية
    list_per_page = 20
    
    def save_model(self, request, obj, form, change):
        # إذا كانت العملية جديدة، احفظ اسم المستخدم الذي أنشأها
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

# ================= باقي الأقسام (الصادر، المرتجعات، التلف، التحويل) =================
# نفس الفكرة السابقة مع تغيير الموديل حسب نوع العملية
# ================= المكونات الفرعية لعمليات الصادر =================
class OutgoingOperationItemInline(admin.TabularInline):
    model = OutgoingOperationItem  # ربط عناصر عملية الصادر
    extra = 1  # عدد الصفوف الفارغة المضافة تلقائياً

class OutgoingOperationFileInline(admin.TabularInline):
    model = OutgoingOperationFile  # ربط ملفات عملية الصادر
    extra = 1

# ================= إدارة عمليات الصادر =================
@admin.register(OutgoingOperation)
class OutgoingOperationAdmin(admin.ModelAdmin):
    list_display = ('paper_number', 'storage', 'beneficiary', 'operation_date', 'created_by', 'created_at')
    list_filter = ('storage', 'beneficiary', 'operation_date')
    search_fields = ('paper_number', 'deliverer_name', 'receiver_name')
    date_hierarchy = 'operation_date'  # إضافة شريط زمني للبحث بالتاريخ
    inlines = [OutgoingOperationItemInline, OutgoingOperationFileInline]  # عرض العناصر والملفات في نفس الصفحة
    list_per_page = 20
    
    def save_model(self, request, obj, form, change):
        # تعيين المستخدم الذي أنشأ العملية إذا كانت جديدة
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

# ================= إدارة عناصر عمليات الصادر =================
@admin.register(OutgoingOperationItem)
class OutgoingOperationItemAdmin(admin.ModelAdmin):
    list_display = ('outgoing_operation', 'category', 'exported_quantity', 'transfer_date', 'actual_transfer_date')
    list_filter = ('category', 'outgoing_operation__storage', 'transfer_date')
    search_fields = ('outgoing_operation__paper_number', 'category__name')
    list_per_page = 20

# ================= إدارة ملفات عمليات الصادر =================
@admin.register(OutgoingOperationFile)
class OutgoingOperationFileAdmin(admin.ModelAdmin):
    list_display = ('file_name', 'outgoing_operation', 'uploaded_at')
    list_filter = ('uploaded_at',)
    search_fields = ('file_name', 'outgoing_operation__paper_number')
    list_per_page = 20

# ================= إدارة تعديلات العمليات (مثلاً تعديل كميات) =================
@admin.register(OperationModification)
class OperationModificationAdmin(admin.ModelAdmin):
    list_display = ('storage', 'operation_type', 'category', 'previous_quantity', 'new_quantity', 'modified_by', 'modification_date')
    list_filter = ('operation_type', 'storage', 'category', 'modification_date')
    search_fields = ('storage__name', 'category__name', 'notes')
    date_hierarchy = 'modification_date'
    list_per_page = 20
    
    def save_model(self, request, obj, form, change):
        # تعيين المستخدم الذي عدل العملية إذا كانت جديدة
        if not change:
            obj.modified_by = request.user
        super().save_model(request, obj, form, change)

# ================= المكونات الفرعية لمرتجعات الوارد =================
class IncomingReturnItemInline(admin.TabularInline):
    model = IncomingReturnItem
    extra = 1

class IncomingReturnFileInline(admin.TabularInline):
    model = IncomingReturnFile
    extra = 1

# ================= إدارة مرتجعات الوارد =================
@admin.register(IncomingReturn)
class IncomingReturnAdmin(admin.ModelAdmin):
    list_display = ('paper_number', 'incoming_operation', 'return_date', 'storage', 'supplier', 'created_by', 'created_at')
    list_filter = ('return_date', 'incoming_operation__storage', 'incoming_operation__supplier')
    search_fields = ('paper_number', 'incoming_operation__paper_number', 'deliverer_name', 'receiver_name')
    date_hierarchy = 'return_date'
    inlines = [IncomingReturnItemInline, IncomingReturnFileInline]
    list_per_page = 20
    
    def storage(self, obj):
        # إرجاع اسم المخزن
        return obj.storage.name
    storage.short_description = 'المخزن'
    
    def supplier(self, obj):
        # إرجاع اسم المورد
        return obj.supplier.full_name
    supplier.short_description = 'المورد'
    
    def save_model(self, request, obj, form, change):
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

# ================= إدارة عناصر مرتجعات الوارد =================
@admin.register(IncomingReturnItem)
class IncomingReturnItemAdmin(admin.ModelAdmin):
    list_display = ('incoming_return', 'category', 'returned_quantity', 'expected_return_date', 'actual_return_date')
    list_filter = ('category', 'expected_return_date', 'actual_return_date')
    search_fields = ('incoming_return__paper_number', 'category__name')
    list_per_page = 20

# ================= المكونات الفرعية لمرتجعات الصادر =================
class OutgoingReturnItemInline(admin.TabularInline):
    model = OutgoingReturnItem
    extra = 1

class OutgoingReturnFileInline(admin.TabularInline):
    model = OutgoingReturnFile
    extra = 1

# ================= إدارة مرتجعات الصادر =================
@admin.register(OutgoingReturn)
class OutgoingReturnAdmin(admin.ModelAdmin):
    list_display = ('paper_number', 'outgoing_operation', 'return_date', 'storage', 'beneficiary', 'created_by', 'created_at')
    list_filter = ('return_date', 'outgoing_operation__storage', 'outgoing_operation__beneficiary')
    search_fields = ('paper_number', 'outgoing_operation__paper_number', 'deliverer_name', 'receiver_name')
    date_hierarchy = 'return_date'
    inlines = [OutgoingReturnItemInline, OutgoingReturnFileInline]
    list_per_page = 20
    
    def storage(self, obj):
        return obj.storage.name
    storage.short_description = 'المخزن'
    
    def beneficiary(self, obj):
        return obj.beneficiary.full_name
    beneficiary.short_description = 'المستفيد'
    
    def save_model(self, request, obj, form, change):
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

# ================= إدارة عناصر مرتجعات الصادر =================
@admin.register(OutgoingReturnItem)
class OutgoingReturnItemAdmin(admin.ModelAdmin):
    list_display = ('outgoing_return', 'category', 'returned_quantity', 'expected_return_date', 'actual_return_date')
    list_filter = ('category', 'expected_return_date', 'actual_return_date')
    search_fields = ('outgoing_return__paper_number', 'category__name')
    list_per_page = 20

# ================= المكونات الفرعية لعمليات التلف =================
class DamageOperationItemInline(admin.TabularInline):
    model = DamageOperationItem
    extra = 1

class DamageOperationFileInline(admin.TabularInline):
    model = DamageOperationFile
    extra = 1

# ================= إدارة عمليات التلف =================
@admin.register(DamageOperation)
class DamageOperationAdmin(admin.ModelAdmin):
    list_display = ('paper_number', 'storage', 'damage_date', 'deliverer_name', 'receiver_name', 'created_by', 'created_at')
    list_filter = ('storage', 'damage_date', 'created_at')
    search_fields = ('paper_number', 'deliverer_name', 'receiver_name', 'statement')
    date_hierarchy = 'damage_date'
    inlines = [DamageOperationItemInline, DamageOperationFileInline]
    list_per_page = 20
    
    def save_model(self, request, obj, form, change):
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

# ================= إدارة عناصر عمليات التلف =================
@admin.register(DamageOperationItem)
class DamageOperationItemAdmin(admin.ModelAdmin):
    list_display = ('damage_operation', 'category', 'damaged_quantity', 'reason', 'notes')
    list_filter = ('category', 'reason', 'damage_operation__storage')
    search_fields = ('damage_operation__paper_number', 'category__name', 'notes')
    list_per_page = 20

# ================= المكونات الفرعية لعمليات التحويل المخزني =================
class StorageTransferItemInline(admin.TabularInline):
    model = StorageTransferItem
    extra = 1

class StorageTransferFileInline(admin.TabularInline):
    model = StorageTransferFile
    extra = 1

# ================= إدارة عمليات التحويل المخزني =================
@admin.register(StorageTransfer)
class StorageTransferAdmin(admin.ModelAdmin):
    list_display = ('paper_number', 'from_storage', 'to_storage', 'transfer_date', 'deliverer_name', 'receiver_name', 'created_by', 'created_at')
    list_filter = ('from_storage', 'to_storage', 'transfer_date', 'created_at')
    search_fields = ('paper_number', 'deliverer_name', 'receiver_name', 'statement')
    date_hierarchy = 'transfer_date'
    inlines = [StorageTransferItemInline, StorageTransferFileInline]
    list_per_page = 20
    
    def save_model(self, request, obj, form, change):
        if not change:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

# ================= إدارة عناصر عمليات التحويل المخزني =================
@admin.register(StorageTransferItem)
class StorageTransferItemAdmin(admin.ModelAdmin):
    list_display = ('storage_transfer', 'category', 'transferred_quantity', 'reason', 'notes')
    list_filter = ('category', 'reason', 'storage_transfer__from_storage', 'storage_transfer__to_storage')
    search_fields = ('storage_transfer__paper_number', 'category__name', 'notes')
    list_per_page = 20
