{% extends 'base.html' %}

{% block title %}لوحة التحكم - نظام إدارة مخازن المحروقات{% endblock %}

{% block content %}
<!-- Header Section -->
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-3 mb-4">
    <div>
        <h1 class="h2 mb-2" style="background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 700;">
            لوحة التحكم
        </h1>
        <p class="text-muted mb-0">نظرة عامة على حالة المخازن والعمليات</p>
    </div>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-primary me-2" onclick="refreshDashboard()">
            <i class="fas fa-sync-alt me-2"></i>تحديث البيانات
        </button>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-download me-2"></i>تصدير التقارير
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#"><i class="fas fa-file-pdf me-2"></i>PDF</a></li>
                <li><a class="dropdown-item" href="#"><i class="fas fa-file-excel me-2"></i>Excel</a></li>
            </ul>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة محسنة -->
<div class="row mb-5">
    <div class="col-xl-2 col-md-4 col-sm-6 mb-4">
        <div class="card stat-card border-right-primary h-100">
            <div class="card-body position-relative">
                <div class="row no-gutters align-items-center">
                    <div class="col">
                        <div class="stat-label mb-2">إجمالي المخازن</div>
                        <div class="stat-number">{{ total_storages }}</div>
                        <div class="mt-2">
                            <small class="text-success">
                                <i class="fas fa-arrow-up me-1"></i>+2 هذا الشهر
                            </small>
                        </div>
                    </div>
                </div>
                <i class="fas fa-warehouse fa-2x"></i>
            </div>
        </div>
    </div>

    <div class="col-xl-2 col-md-4 col-sm-6 mb-4">
        <div class="card stat-card border-right-success h-100">
            <div class="card-body position-relative">
                <div class="row no-gutters align-items-center">
                    <div class="col">
                        <div class="stat-label mb-2">إجمالي الأصناف</div>
                        <div class="stat-number">{{ total_categories }}</div>
                        <div class="mt-2">
                            <small class="text-success">
                                <i class="fas fa-arrow-up me-1"></i>+5 هذا الشهر
                            </small>
                        </div>
                    </div>
                </div>
                <i class="fas fa-tags fa-2x"></i>
            </div>
        </div>
    </div>

    <div class="col-xl-2 col-md-4 col-sm-6 mb-4">
        <div class="card stat-card border-right-info h-100">
            <div class="card-body position-relative">
                <div class="row no-gutters align-items-center">
                    <div class="col">
                        <div class="stat-label mb-2">الموردون</div>
                        <div class="stat-number">{{ total_suppliers }}</div>
                        <div class="mt-2">
                            <small class="text-info">
                                <i class="fas fa-minus me-1"></i>لا تغيير
                            </small>
                        </div>
                    </div>
                </div>
                <i class="fas fa-truck fa-2x"></i>
            </div>
        </div>
    </div>

    <div class="col-xl-2 col-md-4 col-sm-6 mb-4">
        <div class="card stat-card border-right-secondary h-100">
            <div class="card-body position-relative">
                <div class="row no-gutters align-items-center">
                    <div class="col">
                        <div class="stat-label mb-2">المستفيدون</div>
                        <div class="stat-number">{{ total_beneficiaries }}</div>
                        <div class="mt-2">
                            <small class="text-success">
                                <i class="fas fa-arrow-up me-1"></i>+12 هذا الشهر
                            </small>
                        </div>
                    </div>
                </div>
                <i class="fas fa-users fa-2x"></i>
            </div>
        </div>
    </div>

    <div class="col-xl-2 col-md-4 col-sm-6 mb-4">
        <div class="card stat-card border-right-warning h-100">
            <div class="card-body position-relative">
                <div class="row no-gutters align-items-center">
                    <div class="col">
                        <div class="stat-label mb-2">عمليات الوارد</div>
                        <div class="stat-number">{{ total_incoming_operations }}</div>
                        <div class="mt-2">
                            <small class="text-warning">
                                <i class="fas fa-arrow-up me-1"></i>+{{ total_incoming_operations|add:"-15" }} هذا الأسبوع
                            </small>
                        </div>
                    </div>
                </div>
                <i class="fas fa-arrow-down fa-2x"></i>
            </div>
        </div>
    </div>

    <div class="col-xl-2 col-md-4 col-sm-6 mb-4">
        <div class="card stat-card border-right-danger h-100">
            <div class="card-body position-relative">
                <div class="row no-gutters align-items-center">
                    <div class="col">
                        <div class="stat-label mb-2">عمليات الصادر</div>
                        <div class="stat-number">{{ total_outgoing_operations }}</div>
                        <div class="mt-2">
                            <small class="text-danger">
                                <i class="fas fa-arrow-up me-1"></i>+{{ total_outgoing_operations|add:"-8" }} هذا الأسبوع
                            </small>
                        </div>
                    </div>
                </div>
                <i class="fas fa-arrow-up fa-2x"></i>
            </div>
        </div>
    </div>
</div>

<!-- رسم بياني للعمليات -->
<div class="row mb-5">
    <div class="col-lg-8 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>إحصائيات العمليات الشهرية
                </h5>
                <div class="btn-group btn-group-sm" role="group">
                    <input type="radio" class="btn-check" name="chartPeriod" id="monthly" checked>
                    <label class="btn btn-outline-primary" for="monthly">شهري</label>
                    <input type="radio" class="btn-check" name="chartPeriod" id="weekly">
                    <label class="btn btn-outline-primary" for="weekly">أسبوعي</label>
                </div>
            </div>
            <div class="card-body">
                <canvas id="operationsChart" height="100"></canvas>
            </div>
        </div>
    </div>

    <!-- ملخص سريع -->
    <div class="col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tachometer-alt me-2"></i>ملخص سريع
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center mb-3 p-3 rounded" style="background: rgba(37, 99, 235, 0.1);">
                    <div class="icon-wrapper me-3">
                        <i class="fas fa-boxes"></i>
                    </div>
                    <div>
                        <h6 class="mb-1">إجمالي الأصناف المخزنة</h6>
                        <h4 class="mb-0 text-primary">{{ total_storage_items|default:0 }}</h4>
                    </div>
                </div>

                <div class="d-flex align-items-center mb-3 p-3 rounded" style="background: rgba(5, 150, 105, 0.1);">
                    <div class="icon-wrapper success me-3">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div>
                        <h6 class="mb-1">العمليات المكتملة اليوم</h6>
                        <h4 class="mb-0 text-success">{{ today_completed_operations|default:0 }}</h4>
                    </div>
                </div>

                <div class="d-flex align-items-center mb-3 p-3 rounded" style="background: rgba(217, 119, 6, 0.1);">
                    <div class="icon-wrapper warning me-3">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div>
                        <h6 class="mb-1">أصناف تحتاج إعادة تموين</h6>
                        <h4 class="mb-0 text-warning">{{ low_stock_items|default:0 }}</h4>
                    </div>
                </div>

                <div class="d-flex align-items-center p-3 rounded" style="background: rgba(220, 38, 38, 0.1);">
                    <div class="icon-wrapper danger me-3">
                        <i class="fas fa-times-circle"></i>
                    </div>
                    <div>
                        <h6 class="mb-1">أصناف نفدت</h6>
                        <h4 class="mb-0 text-danger">{{ out_of_stock_items|default:0 }}</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- أحدث العمليات -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-arrow-down me-2 text-success"></i>أحدث عمليات الوارد
                </h5>
                <a href="{% url 'fuel_storage:incoming_operations_list' %}" class="btn btn-sm btn-outline-success">
                    عرض الكل <i class="fas fa-arrow-left ms-1"></i>
                </a>
            </div>
            <div class="card-body">
                {% if recent_incoming %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الرقم الورقي</th>
                                    <th>المخزن</th>
                                    <th>المورد</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for operation in recent_incoming %}
                                <tr>
                                    <td>
                                        <span class="badge bg-success">{{ operation.paper_number }}</span>
                                    </td>
                                    <td>{{ operation.storage.name }}</td>
                                    <td>{{ operation.supplier.full_name }}</td>
                                    <td>
                                        <small class="text-muted">{{ operation.operation_date|date:"Y-m-d" }}</small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد عمليات وارد حديثة.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-arrow-up me-2 text-danger"></i>أحدث عمليات الصادر
                </h5>
                <a href="{% url 'fuel_storage:outgoing_operations_list' %}" class="btn btn-sm btn-outline-danger">
                    عرض الكل <i class="fas fa-arrow-left ms-1"></i>
                </a>
            </div>
            <div class="card-body">
                {% if recent_outgoing %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الرقم الورقي</th>
                                    <th>المخزن</th>
                                    <th>المستفيد</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for operation in recent_outgoing %}
                                <tr>
                                    <td>
                                        <span class="badge bg-danger">{{ operation.paper_number }}</span>
                                    </td>
                                    <td>{{ operation.storage.name }}</td>
                                    <td>{{ operation.beneficiary.full_name }}</td>
                                    <td>
                                        <small class="text-muted">{{ operation.operation_date|date:"Y-m-d" }}</small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد عمليات صادر حديثة.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- JavaScript للرسم البياني -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // إعداد الرسم البياني
    const ctx = document.getElementById('operationsChart').getContext('2d');
    const operationsChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
            datasets: [{
                label: 'عمليات الوارد',
                data: [12, 19, 3, 5, 2, 3],
                borderColor: 'rgb(5, 150, 105)',
                backgroundColor: 'rgba(5, 150, 105, 0.1)',
                tension: 0.4,
                fill: true
            }, {
                label: 'عمليات الصادر',
                data: [2, 3, 20, 5, 1, 4],
                borderColor: 'rgb(220, 38, 38)',
                backgroundColor: 'rgba(220, 38, 38, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                },
                title: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    }
                }
            }
        }
    });

    // تحديث البيانات عند تغيير الفترة
    document.querySelectorAll('input[name="chartPeriod"]').forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.id === 'weekly') {
                operationsChart.data.labels = ['الأسبوع 1', 'الأسبوع 2', 'الأسبوع 3', 'الأسبوع 4'];
                operationsChart.data.datasets[0].data = [8, 12, 6, 9];
                operationsChart.data.datasets[1].data = [5, 8, 4, 7];
            } else {
                operationsChart.data.labels = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'];
                operationsChart.data.datasets[0].data = [12, 19, 3, 5, 2, 3];
                operationsChart.data.datasets[1].data = [2, 3, 20, 5, 1, 4];
            }
            operationsChart.update();
        });
    });
});

// دالة تحديث لوحة التحكم
function refreshDashboard() {
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحديث...';
    button.disabled = true;

    // محاكاة تحديث البيانات
    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;

        // إظهار رسالة نجاح
        const alert = document.createElement('div');
        alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
        alert.style.top = '20px';
        alert.style.right = '20px';
        alert.style.zIndex = '9999';
        alert.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>تم تحديث البيانات بنجاح
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(alert);

        setTimeout(() => {
            alert.remove();
        }, 3000);
    }, 2000);
}
</script>
{% endblock %}
