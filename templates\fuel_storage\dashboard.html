{% extends 'base.html' %}

{% block title %}لوحة التحكم - نظام إدارة مخازن المحروقات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">لوحة التحكم</h1>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-xl-2 col-md-4 mb-4">
        <div class="card border-right-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            إجمالي المخازن
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_storages }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-warehouse fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-2 col-md-4 mb-4">
        <div class="card border-right-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            إجمالي الأصناف
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_categories }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-tags fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-2 col-md-4 mb-4">
        <div class="card border-right-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            الموردون
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_suppliers }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-truck fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-2 col-md-4 mb-4">
        <div class="card border-right-secondary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">
                            المستفيدون
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_beneficiaries }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-2 col-md-4 mb-4">
        <div class="card border-right-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            عمليات الوارد
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_incoming_operations }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-arrow-down fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-2 col-md-4 mb-4">
        <div class="card border-right-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            عمليات الصادر
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_outgoing_operations }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-arrow-up fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- أحدث العمليات -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-success">أحدث عمليات الوارد</h6>
            </div>
            <div class="card-body">
                {% if recent_incoming %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الرقم الورقي</th>
                                    <th>المخزن</th>
                                    <th>المورد</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for operation in recent_incoming %}
                                <tr>
                                    <td>{{ operation.paper_number }}</td>
                                    <td>{{ operation.storage.name }}</td>
                                    <td>{{ operation.supplier.full_name }}</td>
                                    <td>{{ operation.operation_date|date:"Y-m-d" }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">لا توجد عمليات وارد حتى الآن</p>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-danger">أحدث عمليات الصادر</h6>
            </div>
            <div class="card-body">
                {% if recent_outgoing %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الرقم الورقي</th>
                                    <th>المخزن</th>
                                    <th>المستفيد</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for operation in recent_outgoing %}
                                <tr>
                                    <td>{{ operation.paper_number }}</td>
                                    <td>{{ operation.storage.name }}</td>
                                    <td>{{ operation.beneficiary.full_name }}</td>
                                    <td>{{ operation.operation_date|date:"Y-m-d" }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">لا توجد عمليات صادر حتى الآن</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
