// JavaScript مخصص لنظام إدارة مخازن المحروقات

document.addEventListener("DOMContentLoaded", () => {
  // تهيئة النظام
  initializeSystem()

  // تهيئة الجداول
  initializeTables()

  // تهيئة النماذج
  initializeForms()

  // تهيئة التقارير
  initializeReports()
})

/**
 * تهيئة النظام العامة
 */
function initializeSystem() {
  // إضافة مؤشرات التحميل للأزرار
  const buttons = document.querySelectorAll('.btn[type="submit"]')
  buttons.forEach((button) => {
    button.addEventListener("click", function () {
      if (this.form && this.form.checkValidity()) {
        showLoadingSpinner(this)
      }
    })
  })

  // تحسين عرض الرسائل
  const alerts = document.querySelectorAll(".alert")
  alerts.forEach((alert) => {
    setTimeout(() => {
      alert.style.opacity = "0"
      setTimeout(() => alert.remove(), 300)
    }, 5000)
  })

  // تحسين التنقل في الشريط الجانبي
  highlightActiveNavItem()
}

/**
 * تهيئة الجداول
 */
function initializeTables() {
  // إضافة فرز للجداول
  const tables = document.querySelectorAll(".table")
  tables.forEach((table) => {
    addTableSorting(table)
    addTableSearch(table)
  })

  // تحسين عرض الأرقام
  const numberCells = document.querySelectorAll(".number-display")
  numberCells.forEach((cell) => {
    const number = Number.parseFloat(cell.textContent)
    if (!isNaN(number)) {
      cell.textContent = number.toLocaleString("ar-SA", {
        minimumFractionDigits: 3,
        maximumFractionDigits: 3,
      })
    }
  })
}

/**
 * تهيئة النماذج
 */
function initializeForms() {
  // التحقق من صحة النماذج
  const forms = document.querySelectorAll("form")
  forms.forEach((form) => {
    form.addEventListener("submit", function (e) {
      if (!validateForm(this)) {
        e.preventDefault()
        e.stopPropagation()
      }
      this.classList.add("was-validated")
    })
  })

  // تحسين حقول التاريخ
  const dateInputs = document.querySelectorAll('input[type="date"]')
  dateInputs.forEach((input) => {
    // تعيين التاريخ الحالي كقيمة افتراضية إذا لم تكن محددة
    if (!input.value) {
      input.value = new Date().toISOString().split("T")[0]
    }
  })

  // تحسين حقول الأرقام
  const numberInputs = document.querySelectorAll('input[type="number"]')
  numberInputs.forEach((input) => {
    input.addEventListener("input", function () {
      if (this.value < 0) {
        this.value = 0
      }
    })
  })
}

/**
 * تهيئة التقارير
 */
function initializeReports() {
  // إضافة وظائف التصدير
  const exportButtons = document.querySelectorAll("[data-export]")
  exportButtons.forEach((button) => {
    button.addEventListener("click", function () {
      const format = this.dataset.export
      const url = new URL(window.location.href)
      url.searchParams.set("export", format)

      showLoadingSpinner(this)
      window.location.href = url.toString()

      setTimeout(() => hideLoadingSpinner(this), 3000)
    })
  })

  // تحسين فلاتر التقارير
  const filterForm = document.querySelector(".report-filters form")
  if (filterForm) {
    const inputs = filterForm.querySelectorAll("input, select")
    inputs.forEach((input) => {
      input.addEventListener("change", () => {
        // تأخير قصير لتحسين الأداء
        setTimeout(() => {
          if (validateReportFilters()) {
            filterForm.submit()
          }
        }, 300)
      })
    })
  }
}

/**
 * إضافة مؤشر التحميل للزر
 */
function showLoadingSpinner(button) {
  const originalText = button.innerHTML
  button.dataset.originalText = originalText
  button.innerHTML = '<span class="loading-spinner"></span> جاري المعالجة...'
  button.disabled = true
}

/**
 * إخفاء مؤشر التحميل من الزر
 */
function hideLoadingSpinner(button) {
  if (button.dataset.originalText) {
    button.innerHTML = button.dataset.originalText
    button.disabled = false
  }
}

/**
 * تمييز العنصر النشط في القائمة الجانبية
 */
function highlightActiveNavItem() {
  const currentPath = window.location.pathname
  const navLinks = document.querySelectorAll(".sidebar .nav-link")

  navLinks.forEach((link) => {
    link.classList.remove("active")
    if (link.getAttribute("href") === currentPath) {
      link.classList.add("active")
    }
  })
}

/**
 * إضافة وظيفة الفرز للجدول
 */
function addTableSorting(table) {
  const headers = table.querySelectorAll("th")
  headers.forEach((header, index) => {
    if (header.textContent.trim()) {
      header.style.cursor = "pointer"
      header.addEventListener("click", () => sortTable(table, index))
    }
  })
}

/**
 * فرز الجدول حسب العمود
 */
function sortTable(table, columnIndex) {
  const tbody = table.querySelector("tbody")
  const rows = Array.from(tbody.querySelectorAll("tr"))

  const isAscending = table.dataset.sortDirection !== "asc"
  table.dataset.sortDirection = isAscending ? "asc" : "desc"

  rows.sort((a, b) => {
    const aText = a.cells[columnIndex].textContent.trim()
    const bText = b.cells[columnIndex].textContent.trim()

    // محاولة تحويل إلى رقم
    const aNum = Number.parseFloat(aText)
    const bNum = Number.parseFloat(bText)

    if (!isNaN(aNum) && !isNaN(bNum)) {
      return isAscending ? aNum - bNum : bNum - aNum
    }

    // مقارنة نصية
    return isAscending ? aText.localeCompare(bText, "ar") : bText.localeCompare(aText, "ar")
  })

  // إعادة ترتيب الصفوف
  rows.forEach((row) => tbody.appendChild(row))

  // تحديث مؤشر الفرز
  updateSortIndicator(table, columnIndex, isAscending)
}

/**
 * تحديث مؤشر الفرز في رأس الجدول
 */
function updateSortIndicator(table, columnIndex, isAscending) {
  const headers = table.querySelectorAll("th")
  headers.forEach((header, index) => {
    const icon = header.querySelector(".sort-icon")
    if (icon) icon.remove()

    if (index === columnIndex) {
      const sortIcon = document.createElement("i")
      sortIcon.className = `fas fa-sort-${isAscending ? "up" : "down"} sort-icon ms-1`
      header.appendChild(sortIcon)
    }
  })
}

/**
 * إضافة وظيفة البحث للجدول
 */
function addTableSearch(table) {
  const searchContainer = document.createElement("div")
  searchContainer.className = "table-search mb-3"
  searchContainer.innerHTML = `
        <div class="input-group">
            <input type="text" class="form-control" placeholder="البحث في الجدول...">
            <span class="input-group-text">
                <i class="fas fa-search"></i>
            </span>
        </div>
    `

  table.parentNode.insertBefore(searchContainer, table)

  const searchInput = searchContainer.querySelector("input")
  searchInput.addEventListener("input", () => filterTable(table, searchInput.value))
}

/**
 * فلترة الجدول حسب النص المدخل
 */
function filterTable(table, searchText) {
  const rows = table.querySelectorAll("tbody tr")
  const searchLower = searchText.toLowerCase()

  rows.forEach((row) => {
    const text = row.textContent.toLowerCase()
    row.style.display = text.includes(searchLower) ? "" : "none"
  })
}

/**
 * التحقق من صحة النموذج
 */
function validateForm(form) {
  let isValid = true

  // التحقق من الحقول المطلوبة
  const requiredFields = form.querySelectorAll("[required]")
  requiredFields.forEach((field) => {
    if (!field.value.trim()) {
      showFieldError(field, "هذا الحقل مطلوب")
      isValid = false
    } else {
      hideFieldError(field)
    }
  })

  // التحقق من صحة الأرقام
  const numberFields = form.querySelectorAll('input[type="number"]')
  numberFields.forEach((field) => {
    if (field.value && (isNaN(field.value) || Number.parseFloat(field.value) < 0)) {
      showFieldError(field, "يجب إدخال رقم صحيح")
      isValid = false
    }
  })

  // التحقق من صحة التواريخ
  const dateFields = form.querySelectorAll('input[type="date"]')
  dateFields.forEach((field) => {
    if (field.value && !isValidDate(field.value)) {
      showFieldError(field, "يجب إدخال تاريخ صحيح")
      isValid = false
    }
  })

  return isValid
}

/**
 * عرض رسالة خطأ للحقل
 */
function showFieldError(field, message) {
  hideFieldError(field)

  const errorDiv = document.createElement("div")
  errorDiv.className = "invalid-feedback d-block"
  errorDiv.textContent = message

  field.classList.add("is-invalid")
  field.parentNode.appendChild(errorDiv)
}

/**
 * إخفاء رسالة خطأ الحقل
 */
function hideFieldError(field) {
  field.classList.remove("is-invalid")
  const errorDiv = field.parentNode.querySelector(".invalid-feedback")
  if (errorDiv) {
    errorDiv.remove()
  }
}

/**
 * التحقق من صحة التاريخ
 */
function isValidDate(dateString) {
  const date = new Date(dateString)
  return date instanceof Date && !isNaN(date)
}

/**
 * التحقق من صحة فلاتر التقارير
 */
function validateReportFilters() {
  const fromDate = document.querySelector('input[name="from_date"]')
  const toDate = document.querySelector('input[name="to_date"]')

  if (fromDate && toDate && fromDate.value && toDate.value) {
    if (new Date(fromDate.value) > new Date(toDate.value)) {
      alert("تاريخ البداية يجب أن يكون قبل تاريخ النهاية")
      return false
    }
  }

  return true
}

/**
 * تحسين عرض الأرقام العربية
 */
function formatArabicNumbers() {
  const numberElements = document.querySelectorAll(".number-display")
  numberElements.forEach((element) => {
    const number = Number.parseFloat(element.textContent)
    if (!isNaN(number)) {
      element.textContent = number.toLocaleString("ar-SA")
    }
  })
}

/**
 * إضافة تأثيرات بصرية للبطاقات
 */
function addCardAnimations() {
  const cards = document.querySelectorAll(".card")
  cards.forEach((card) => {
    card.addEventListener("mouseenter", function () {
      this.style.transform = "translateY(-2px)"
      this.style.transition = "transform 0.2s ease"
    })

    card.addEventListener("mouseleave", function () {
      this.style.transform = "translateY(0)"
    })
  })
}

/**
 * تحسين التجاوب للأجهزة المحمولة
 */
function improveMobileExperience() {
  if (window.innerWidth <= 768) {
    // تحسين الجداول للأجهزة المحمولة
    const tables = document.querySelectorAll(".table")
    tables.forEach((table) => {
      table.style.fontSize = "0.875rem"
    })

    // تحسين الأزرار للأجهزة المحمولة
    const buttons = document.querySelectorAll(".btn")
    buttons.forEach((button) => {
      button.style.padding = "0.5rem 1rem"
    })
  }
}

// تشغيل التحسينات عند تحميل الصفحة
window.addEventListener("load", () => {
  formatArabicNumbers()
  addCardAnimations()
  improveMobileExperience()
})

// إعادة تشغيل التحسينات عند تغيير حجم النافذة
window.addEventListener("resize", () => {
  improveMobileExperience()
})

// تصدير الوظائف للاستخدام العام
window.FuelStorageSystem = {
  showLoadingSpinner,
  hideLoadingSpinner,
  validateForm,
  formatArabicNumbers,
  sortTable,
  filterTable,
}
