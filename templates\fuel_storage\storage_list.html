{% extends 'base.html' %}

{% block title %}المخازن - نظام إدارة مخازن المحروقات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">المخازن</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="/admin/fuel_storage/storage/add/" class="btn btn-sm btn-outline-secondary">
            <i class="fas fa-plus me-1"></i>
            إضافة مخزن جديد
        </a>
    </div>
</div>

{% if storages %}
    <div class="row">
        {% for storage in storages %}
        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card stat-card">
                <div class="card-body">
                    <h5 class="card-title">
                        {{ storage.name }}
                        <span class="badge bg-{% if storage.classification == 'main' %}primary{% elif storage.classification == 'secondary' %}success{% else %}warning{% endif %} ms-2">
                            {{ storage.get_classification_display }}
                        </span>
                    </h5>
                    <p class="card-text">
                        <i class="fas fa-user me-2"></i><strong>أمين المخزن:</strong> {{ storage.keeper_name }}<br>
                        <i class="fas fa-phone me-2"></i><strong>الهاتف:</strong> {{ storage.phone_number }}<br>
                        <i class="fas fa-calendar me-2"></i><strong>تاريخ الإنشاء:</strong> {{ storage.created_at|date:"Y-m-d" }}
                    </p>
                    <div class="d-flex justify-content-between">
                        <a href="/admin/fuel_storage/storage/{{ storage.id }}/change/" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-edit me-1"></i>تعديل
                        </a>
                        <a href="{% url 'fuel_storage:storage_report' %}?storage_id={{ storage.id }}" class="btn btn-sm btn-outline-info">
                            <i class="fas fa-chart-bar me-1"></i>التقرير
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
{% else %}
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        لا توجد مخازن مسجلة حتى الآن.
    </div>
{% endif %}
{% endblock %}
