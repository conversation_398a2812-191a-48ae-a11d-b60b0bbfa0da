{% extends 'base.html' %}

{% block title %}المخازن - نظام إدارة مخازن المحروقات{% endblock %}

{% block content %}
<!-- Header Section -->
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-3 mb-4">
    <div>
        <h1 class="h2 mb-2" style="background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 700;">
            <i class="fas fa-warehouse me-2"></i>إدارة المخازن
        </h1>
        <p class="text-muted mb-0">عرض وإدارة جميع المخازن في النظام</p>
    </div>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2" role="group">
            <button type="button" class="btn btn-outline-secondary" onclick="toggleView('cards')" id="cardsViewBtn">
                <i class="fas fa-th-large me-1"></i>بطاقات
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="toggleView('table')" id="tableViewBtn">
                <i class="fas fa-table me-1"></i>جدول
            </button>
        </div>
        <a href="/admin/fuel_storage/storage/add/" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>إضافة مخزن جديد
        </a>
    </div>
</div>

<!-- Search and Filter Section -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="input-group">
            <span class="input-group-text">
                <i class="fas fa-search"></i>
            </span>
            <input type="text" class="form-control" placeholder="البحث في المخازن..." id="searchInput">
        </div>
    </div>
    <div class="col-md-3">
        <select class="form-select" id="classificationFilter">
            <option value="">جميع التصنيفات</option>
            <option value="main">مخزن رئيسي</option>
            <option value="secondary">مخزن فرعي</option>
            <option value="temporary">مخزن مؤقت</option>
        </select>
    </div>
    <div class="col-md-3">
        <button type="button" class="btn btn-outline-primary w-100" onclick="exportStorages()">
            <i class="fas fa-download me-2"></i>تصدير البيانات
        </button>
    </div>
</div>

{% if storages %}
    <!-- Cards View -->
    <div id="cardsView" class="row">
        {% for storage in storages %}
        <div class="col-md-6 col-lg-4 mb-4 storage-item" data-classification="{{ storage.classification }}" data-name="{{ storage.name|lower }}">
            <div class="card stat-card h-100">
                <div class="card-body position-relative">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-warehouse me-2 text-primary"></i>{{ storage.name }}
                        </h5>
                        <span class="badge bg-{% if storage.classification == 'main' %}primary{% elif storage.classification == 'secondary' %}success{% else %}warning{% endif %}">
                            {{ storage.get_classification_display }}
                        </span>
                    </div>

                    <div class="storage-info">
                        <div class="info-item mb-2">
                            <i class="fas fa-user text-muted me-2"></i>
                            <span class="text-muted">أمين المخزن:</span>
                            <strong>{{ storage.keeper_name }}</strong>
                        </div>
                        <div class="info-item mb-2">
                            <i class="fas fa-phone text-muted me-2"></i>
                            <span class="text-muted">الهاتف:</span>
                            <strong>{{ storage.phone_number }}</strong>
                        </div>
                        <div class="info-item mb-3">
                            <i class="fas fa-calendar text-muted me-2"></i>
                            <span class="text-muted">تاريخ الإنشاء:</span>
                            <strong>{{ storage.created_at|date:"Y-m-d" }}</strong>
                        </div>
                    </div>

                    <!-- Storage Stats -->
                    <div class="row text-center mb-3">
                        <div class="col-4">
                            <div class="stat-mini">
                                <div class="stat-mini-number">{{ storage.storage_items.count|default:0 }}</div>
                                <div class="stat-mini-label">أصناف</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stat-mini">
                                <div class="stat-mini-number">{{ storage.incoming_operations.count|default:0 }}</div>
                                <div class="stat-mini-label">وارد</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stat-mini">
                                <div class="stat-mini-number">{{ storage.outgoing_operations.count|default:0 }}</div>
                                <div class="stat-mini-label">صادر</div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex gap-2">
                        <a href="/admin/fuel_storage/storage/{{ storage.id }}/change/" class="btn btn-sm btn-outline-primary flex-fill">
                            <i class="fas fa-edit me-1"></i>تعديل
                        </a>
                        <a href="{% url 'fuel_storage:storage_report' %}?storage_id={{ storage.id }}" class="btn btn-sm btn-outline-info flex-fill">
                            <i class="fas fa-chart-bar me-1"></i>التقرير
                        </a>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#"><i class="fas fa-eye me-2"></i>عرض التفاصيل</a></li>
                                <li><a class="dropdown-item" href="#"><i class="fas fa-copy me-2"></i>نسخ المعلومات</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#"><i class="fas fa-trash me-2"></i>حذف</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Table View -->
    <div id="tableView" class="d-none">
        <div class="card">
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>اسم المخزن</th>
                                <th>التصنيف</th>
                                <th>أمين المخزن</th>
                                <th>الهاتف</th>
                                <th>عدد الأصناف</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for storage in storages %}
                            <tr class="storage-item" data-classification="{{ storage.classification }}" data-name="{{ storage.name|lower }}">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-warehouse text-primary me-2"></i>
                                        <strong>{{ storage.name }}</strong>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-{% if storage.classification == 'main' %}primary{% elif storage.classification == 'secondary' %}success{% else %}warning{% endif %}">
                                        {{ storage.get_classification_display }}
                                    </span>
                                </td>
                                <td>{{ storage.keeper_name }}</td>
                                <td>{{ storage.phone_number }}</td>
                                <td>
                                    <span class="badge bg-info">{{ storage.storage_items.count|default:0 }}</span>
                                </td>
                                <td>{{ storage.created_at|date:"Y-m-d" }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="/admin/fuel_storage/storage/{{ storage.id }}/change/" class="btn btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'fuel_storage:storage_report' %}?storage_id={{ storage.id }}" class="btn btn-outline-info">
                                            <i class="fas fa-chart-bar"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
{% else %}
    <div class="text-center py-5">
        <i class="fas fa-warehouse fa-4x text-muted mb-3"></i>
        <h4 class="text-muted">لا توجد مخازن مسجلة</h4>
        <p class="text-muted mb-4">ابدأ بإضافة مخزن جديد لإدارة المحروقات</p>
        <a href="/admin/fuel_storage/storage/add/" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>إضافة مخزن جديد
        </a>
    </div>
{% endif %}
<!-- Custom CSS for this page -->
<style>
.stat-mini {
    padding: 0.5rem;
    border-radius: 0.5rem;
    background: rgba(37, 99, 235, 0.1);
}

.stat-mini-number {
    font-size: 1.25rem;
    font-weight: 700;
    color: #2563eb;
}

.stat-mini-label {
    font-size: 0.75rem;
    color: #64748b;
    font-weight: 500;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.storage-item {
    transition: all 0.3s ease;
}

.storage-item.filtered-out {
    display: none !important;
}

#cardsViewBtn.active,
#tableViewBtn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}
</style>

<!-- JavaScript for interactivity -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set initial view
    document.getElementById('cardsViewBtn').classList.add('active');

    // Search functionality
    const searchInput = document.getElementById('searchInput');
    const classificationFilter = document.getElementById('classificationFilter');

    function filterStorages() {
        const searchTerm = searchInput.value.toLowerCase();
        const selectedClassification = classificationFilter.value;
        const storageItems = document.querySelectorAll('.storage-item');

        storageItems.forEach(item => {
            const name = item.dataset.name;
            const classification = item.dataset.classification;

            const matchesSearch = name.includes(searchTerm);
            const matchesClassification = !selectedClassification || classification === selectedClassification;

            if (matchesSearch && matchesClassification) {
                item.classList.remove('filtered-out');
            } else {
                item.classList.add('filtered-out');
            }
        });
    }

    searchInput.addEventListener('input', filterStorages);
    classificationFilter.addEventListener('change', filterStorages);
});

// Toggle between cards and table view
function toggleView(viewType) {
    const cardsView = document.getElementById('cardsView');
    const tableView = document.getElementById('tableView');
    const cardsBtn = document.getElementById('cardsViewBtn');
    const tableBtn = document.getElementById('tableViewBtn');

    if (viewType === 'cards') {
        cardsView.classList.remove('d-none');
        tableView.classList.add('d-none');
        cardsBtn.classList.add('active');
        tableBtn.classList.remove('active');
    } else {
        cardsView.classList.add('d-none');
        tableView.classList.remove('d-none');
        cardsBtn.classList.remove('active');
        tableBtn.classList.add('active');
    }
}

// Export functionality
function exportStorages() {
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التصدير...';
    button.disabled = true;

    // Simulate export process
    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;

        // Show success message
        const alert = document.createElement('div');
        alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
        alert.style.top = '20px';
        alert.style.right = '20px';
        alert.style.zIndex = '9999';
        alert.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>تم تصدير البيانات بنجاح
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(alert);

        setTimeout(() => {
            alert.remove();
        }, 3000);
    }, 2000);
}
</script>

{% endblock %}
