from django.db import models
from django.contrib.auth.models import AbstractUser
from django.core.validators import RegexValidator
from decimal import Decimal
from django.core.exceptions import ValidationError
from django.db.models import ProtectedError

class CustomUser(AbstractUser):
    USER_TYPES = [
        ('manager', 'مدير'),
        ('operator', 'مشغل'),
    ]
    
    full_name = models.CharField(max_length=100, verbose_name='الاسم الكامل')
    user_type = models.CharField(max_length=20, choices=USER_TYPES, default='operator', verbose_name='نوع المستخدم')
    
    class Meta:
        verbose_name = 'مستخدم'
        verbose_name_plural = 'المستخدمون'

class Category(models.Model):
    name = models.CharField(max_length=100, unique=True, verbose_name='اسم الصنف')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    
    def delete(self, *args, **kwargs):
        # التحقق من وجود عمليات مرتبطة
        if (self.incomingoperationitem_set.exists() or 
            self.outgoingoperationitem_set.exists() or
            self.incomingreturnitem_set.exists() or
            self.outgoingreturnitem_set.exists() or
            self.damageoperationitem_set.exists() or
            self.storagetransferitem_set.exists()):
            # بدلاً من الحذف، قم بإلغاء التفعيل
            self.is_active = False
            self.save()
            raise ProtectedError("لا يمكن حذف هذا الصنف لأنه مرتبط بعمليات. تم إلغاء تفعيله بدلاً من ذلك.", [self])
        super().delete(*args, **kwargs)
    
    def __str__(self):
        status = " (غير نشط)" if not self.is_active else ""
        return f"{self.name}{status}"
    
    class Meta:
        verbose_name = 'صنف'
        verbose_name_plural = 'الأصناف'

class Station(models.Model):
    name = models.CharField(max_length=100, verbose_name='اسم المحطة')
    address = models.TextField(verbose_name='العنوان')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    
    def delete(self, *args, **kwargs):
        if self.incomingoperation_set.exists():
            self.is_active = False
            self.save()
            raise ProtectedError("لا يمكن حذف هذه المحطة لأنها مرتبطة بعمليات. تم إلغاء تفعيلها بدلاً من ذلك.", [self])
        super().delete(*args, **kwargs)
    
    def __str__(self):
        status = " (غير نشط)" if not self.is_active else ""
        return f"{self.name}{status}"
    
    class Meta:
        verbose_name = 'محطة'
        verbose_name_plural = 'المحطات'

class Supplier(models.Model):
    phone_regex = RegexValidator(regex=r'^\+?1?\d{9,15}$', message="رقم الهاتف يجب أن يكون بالصيغة: '+999999999'. حتى 15 رقم مسموح.")
    
    full_name = models.CharField(max_length=100, verbose_name='الاسم الكامل')
    phone_number = models.CharField(validators=[phone_regex], max_length=17, verbose_name='رقم الهاتف')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    
    def delete(self, *args, **kwargs):
        if self.incomingoperation_set.exists():
            self.is_active = False
            self.save()
            raise ProtectedError("لا يمكن حذف هذا المورد لأنه مرتبط بعمليات. تم إلغاء تفعيله بدلاً من ذلك.", [self])
        super().delete(*args, **kwargs)
    
    def __str__(self):
        status = " (غير نشط)" if not self.is_active else ""
        return f"{self.full_name}{status}"
    
    class Meta:
        verbose_name = 'مورد'
        verbose_name_plural = 'الموردون'

class Beneficiary(models.Model):
    phone_regex = RegexValidator(regex=r'^\+?1?\d{9,15}$', message="رقم الهاتف يجب أن يكون بالصيغة: '+999999999'. حتى 15 رقم مسموح.")
    
    full_name = models.CharField(max_length=100, verbose_name='الاسم الكامل')
    phone_number = models.CharField(validators=[phone_regex], max_length=17, verbose_name='رقم الهاتف')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    
    def delete(self, *args, **kwargs):
        if self.outgoingoperation_set.exists():
            self.is_active = False
            self.save()
            raise ProtectedError("لا يمكن حذف هذا المستفيد لأنه مرتبط بعمليات. تم إلغاء تفعيله بدلاً من ذلك.", [self])
        super().delete(*args, **kwargs)
    
    def __str__(self):
        status = " (غير نشط)" if not self.is_active else ""
        return f"{self.full_name}{status}"
    
    class Meta:
        verbose_name = 'مستفيد'
        verbose_name_plural = 'المستفيدون'

class Storage(models.Model):
    CLASSIFICATION_CHOICES = [
        ('main', 'مخزن رئيسي'),
        ('secondary', 'مخزن فرعي'),
        ('temporary', 'مخزن مؤقت'),
    ]
    
    phone_regex = RegexValidator(regex=r'^\+?1?\d{9,15}$', message="رقم الهاتف يجب أن يكون بالصيغة: '+999999999'. حتى 15 رقم مسموح.")
    
    name = models.CharField(max_length=100, unique=True, verbose_name='اسم المخزن')
    classification = models.CharField(max_length=20, choices=CLASSIFICATION_CHOICES, verbose_name='التصنيف')
    keeper_name = models.CharField(max_length=100, verbose_name='أمين المخزن')
    phone_number = models.CharField(validators=[phone_regex], max_length=17, verbose_name='هاتف المخزن')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    
    def delete(self, *args, **kwargs):
        if (self.incomingoperation_set.exists() or 
            self.outgoingoperation_set.exists() or
            self.damageoperation_set.exists() or
            self.transfers_from.exists() or
            self.transfers_to.exists()):
            self.is_active = False
            self.save()
            raise ProtectedError("لا يمكن حذف هذا المخزن لأنه مرتبط بعمليات. تم إلغاء تفعيله بدلاً من ذلك.", [self])
        super().delete(*args, **kwargs)
    
    def __str__(self):
        status = " (غير نشط)" if not self.is_active else ""
        return f"{self.name}{status}"
    
    class Meta:
        verbose_name = 'مخزن'
        verbose_name_plural = 'المخازن'

class StorageItem(models.Model):
    UNIT_CHOICES = [
        ('liter', 'لتر'),
        ('gallon', 'جالون'),
        ('barrel', 'برميل'),
        ('ton', 'طن'),
        ('kg', 'كيلوجرام'),
    ]
    
    storage = models.ForeignKey(Storage, on_delete=models.CASCADE, verbose_name='المخزن')
    category = models.ForeignKey(Category, on_delete=models.CASCADE, verbose_name='الصنف')
    unit_of_measure = models.CharField(max_length=20, choices=UNIT_CHOICES, verbose_name='وحدة القياس')
    opening_balance = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name='الرصيد الافتتاحي')
    current_quantity = models.DecimalField(max_digits=15, decimal_places=3, default=0, verbose_name='الكمية الحالية')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')
    
    def __str__(self):
        return f"{self.storage.name} - {self.category.name}"
    
    class Meta:
        verbose_name = 'صنف مخزن'
        verbose_name_plural = 'أصناف المخازن'
        unique_together = ['storage', 'category']

class IncomingOperation(models.Model):
    storage = models.ForeignKey(Storage, on_delete=models.PROTECT, verbose_name='المخزن')
    operation_date = models.DateTimeField(verbose_name='تاريخ عملية الوارد')
    paper_number = models.CharField(max_length=50, verbose_name='الرقم الورقي')
    supplier = models.ForeignKey(Supplier, on_delete=models.PROTECT, verbose_name='المورد')
    station = models.ForeignKey(Station, on_delete=models.PROTECT, verbose_name='المحطة')
    supply_document_number = models.CharField(max_length=50, verbose_name='رقم سند التوريد')
    
    # بيانات المسلم
    deliverer_name = models.CharField(max_length=100, verbose_name='اسم المسلم')
    deliverer_job_number = models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمسلم')
    
    # بيانات المستلم
    receiver_name = models.CharField(max_length=100, verbose_name='اسم المستلم')
    receiver_job_number = models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمستلم')
    
    statement = models.TextField(verbose_name='البيان')
    is_locked = models.BooleanField(default=False, verbose_name='مقفل للتعديل')
    created_by = models.ForeignKey(CustomUser, on_delete=models.PROTECT, verbose_name='أنشئ بواسطة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    
    def save(self, *args, **kwargs):
        # قفل العملية بعد الحفظ الأول
        if self.pk:
            self.is_locked = True
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"وارد {self.paper_number} - {self.storage.name}"
    
    class Meta:
        verbose_name = 'عملية وارد'
        verbose_name_plural = 'عمليات الوارد'

class IncomingOperationItem(models.Model):
    incoming_operation = models.ForeignKey(IncomingOperation, on_delete=models.CASCADE, related_name='items', verbose_name='عملية الوارد')
    category = models.ForeignKey(Category, on_delete=models.PROTECT, verbose_name='الصنف')
    imported_quantity = models.DecimalField(max_digits=15, decimal_places=3, verbose_name='الكمية المستوردة')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    
    def save(self, *args, **kwargs):
        # التحقق من قفل العملية
        if self.incoming_operation.is_locked and self.pk:
            raise ValidationError("لا يمكن تعديل هذه العملية لأنها مقفلة")
        
        super().save(*args, **kwargs)
        # تحديث الكمية الحالية في المخزن
        storage_item, created = StorageItem.objects.get_or_create(
            storage=self.incoming_operation.storage,
            category=self.category,
            defaults={
                'unit_of_measure': 'liter',  # وحدة افتراضية
                'opening_balance': 0,
                'current_quantity': 0
            }
        )
        storage_item.current_quantity += self.imported_quantity
        storage_item.save()
    
    def __str__(self):
        return f"{self.category.name} - {self.imported_quantity}"
    
    class Meta:
        verbose_name = 'صنف وارد'
        verbose_name_plural = 'أصناف الوارد'

class IncomingOperationFile(models.Model):
    incoming_operation = models.ForeignKey(IncomingOperation, on_delete=models.CASCADE, related_name='files', verbose_name='عملية الوارد')
    file = models.FileField(upload_to='incoming_operations/', verbose_name='الملف')
    file_name = models.CharField(max_length=255, verbose_name='اسم الملف')
    uploaded_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')
    
    def __str__(self):
        return self.file_name
    
    class Meta:
        verbose_name = 'ملف مرفق'
        verbose_name_plural = 'الملفات المرفقة'

# نماذج عمليات الصادر
class OutgoingOperation(models.Model):
    storage = models.ForeignKey(Storage, on_delete=models.PROTECT, verbose_name='المخزن')
    operation_date = models.DateTimeField(verbose_name='تاريخ عملية الصادر')
    paper_number = models.CharField(max_length=50, verbose_name='الرقم الورقي')
    beneficiary = models.ForeignKey(Beneficiary, on_delete=models.PROTECT, verbose_name='المستفيد')
    
    # بيانات المسلم
    deliverer_name = models.CharField(max_length=100, verbose_name='اسم المسلم')
    deliverer_job_number = models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمسلم')
    
    # بيانات المستلم
    receiver_name = models.CharField(max_length=100, verbose_name='اسم المستلم')
    receiver_job_number = models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمستلم')
    
    statement = models.TextField(verbose_name='البيان')
    is_locked = models.BooleanField(default=False, verbose_name='مقفل للتعديل')
    created_by = models.ForeignKey(CustomUser, on_delete=models.PROTECT, verbose_name='أنشئ بواسطة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    
    def save(self, *args, **kwargs):
        if self.pk:
            self.is_locked = True
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"صادر {self.paper_number} - {self.storage.name}"
    
    class Meta:
        verbose_name = 'عملية صادر'
        verbose_name_plural = 'عمليات الصادر'

class OutgoingOperationItem(models.Model):
    outgoing_operation = models.ForeignKey(OutgoingOperation, on_delete=models.CASCADE, related_name='items', verbose_name='عملية الصادر')
    category = models.ForeignKey(Category, on_delete=models.PROTECT, verbose_name='الصنف')
    exported_quantity = models.DecimalField(max_digits=15, decimal_places=3, verbose_name='الكمية المصروفة')
    transfer_date = models.DateField(verbose_name='تاريخ التحويل')
    actual_transfer_date = models.DateField(blank=True, null=True, verbose_name='تاريخ التحويل الفعلي')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    
    def clean(self):
        # التحقق من توفر الكمية في المخزن
        try:
            storage_item = StorageItem.objects.get(
                storage=self.outgoing_operation.storage,
                category=self.category
            )
            if storage_item.current_quantity < self.exported_quantity:
                raise ValidationError(f'الكمية المطلوبة ({self.exported_quantity}) أكبر من الكمية المتوفرة ({storage_item.current_quantity})')
        except StorageItem.DoesNotExist:
            raise ValidationError(f'الصنف {self.category.name} غير موجود في المخزن {self.outgoing_operation.storage.name}')
    
    def save(self, *args, **kwargs):
        # التحقق من قفل العملية
        if self.outgoing_operation.is_locked and self.pk:
            raise ValidationError("لا يمكن تعديل هذه العملية لأنها مقفلة")
        
        self.clean()
        super().save(*args, **kwargs)
        # خصم الكمية من المخزن
        storage_item = StorageItem.objects.get(
            storage=self.outgoing_operation.storage,
            category=self.category
        )
        storage_item.current_quantity -= self.exported_quantity
        storage_item.save()
    
    def __str__(self):
        return f"{self.category.name} - {self.exported_quantity}"
    
    class Meta:
        verbose_name = 'صنف صادر'
        verbose_name_plural = 'أصناف الصادر'

class OutgoingOperationFile(models.Model):
    outgoing_operation = models.ForeignKey(OutgoingOperation, on_delete=models.CASCADE, related_name='files', verbose_name='عملية الصادر')
    file = models.FileField(upload_to='outgoing_operations/', verbose_name='الملف')
    file_name = models.CharField(max_length=255, verbose_name='اسم الملف')
    uploaded_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')
    
    def __str__(self):
        return self.file_name
    
    class Meta:
        verbose_name = 'ملف مرفق للصادر'
        verbose_name_plural = 'الملفات المرفقة للصادر'

# نموذج تعديل العمليات
class OperationModification(models.Model):
    OPERATION_TYPES = [
        ('incoming', 'وارد'),
        ('outgoing', 'صادر'),
    ]
    
    storage = models.ForeignKey(Storage, on_delete=models.PROTECT, verbose_name='المخزن')
    operation_type = models.CharField(max_length=20, choices=OPERATION_TYPES, verbose_name='نوع العملية')
    operation_date = models.DateTimeField(verbose_name='تاريخ العملية')
    category = models.ForeignKey(Category, on_delete=models.PROTECT, verbose_name='الصنف')
    
    # الكميات
    previous_quantity = models.DecimalField(max_digits=15, decimal_places=3, verbose_name='الكمية السابقة')
    new_quantity = models.DecimalField(max_digits=15, decimal_places=3, verbose_name='الكمية الجديدة')
    
    # ربط بالعمليات الأصلية
    incoming_operation = models.ForeignKey(IncomingOperation, on_delete=models.CASCADE, blank=True, null=True, verbose_name='عملية الوارد')
    outgoing_operation = models.ForeignKey(OutgoingOperation, on_delete=models.CASCADE, blank=True, null=True, verbose_name='عملية الصادر')
    
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    modified_by = models.ForeignKey(CustomUser, on_delete=models.PROTECT, verbose_name='عُدل بواسطة')
    modification_date = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التعديل')
    
    def clean(self):
        # التحقق من أن نوع العملية يتطابق مع العملية المرتبطة
        if self.operation_type == 'incoming' and not self.incoming_operation:
            raise ValidationError('يجب تحديد عملية الوارد عند اختيار نوع العملية "وارد"')
        if self.operation_type == 'outgoing' and not self.outgoing_operation:
            raise ValidationError('يجب تحديد عملية الصادر عند اختيار نوع العملية "صادر"')
    
    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)
        
        # تحديث الكمية في المخزن بناءً على الفرق
        quantity_difference = self.new_quantity - self.previous_quantity
        
        storage_item = StorageItem.objects.get(
            storage=self.storage,
            category=self.category
        )
        
        if self.operation_type == 'incoming':
            # في حالة الوارد: الزيادة تعني إضافة للمخزن
            storage_item.current_quantity += quantity_difference
        else:  # outgoing
            # في حالة الصادر: الزيادة تعني خصم أكثر من المخزن
            storage_item.current_quantity -= quantity_difference
        
        storage_item.save()
        
        # تحديث العملية الأصلية
        if self.operation_type == 'incoming' and self.incoming_operation:
            item = IncomingOperationItem.objects.get(
                incoming_operation=self.incoming_operation,
                category=self.category
            )
            item.imported_quantity = self.new_quantity
            item.save()
        elif self.operation_type == 'outgoing' and self.outgoing_operation:
            item = OutgoingOperationItem.objects.get(
                outgoing_operation=self.outgoing_operation,
                category=self.category
            )
            item.exported_quantity = self.new_quantity
            item.save()
    
    def __str__(self):
        operation_name = self.get_operation_type_display()
        return f"تعديل {operation_name} - {self.category.name} - {self.storage.name}"
    
    class Meta:
        verbose_name = 'تعديل عملية'
        verbose_name_plural = 'تعديلات العمليات'

# نماذج مرتجع الوارد
class IncomingReturn(models.Model):
    incoming_operation = models.ForeignKey(IncomingOperation, on_delete=models.PROTECT, verbose_name='عملية الوارد المراد الإرجاع منها')
    return_date = models.DateTimeField(verbose_name='تاريخ المرتجع')
    paper_number = models.CharField(max_length=50, verbose_name='الرقم الورقي')
    
    # بيانات المسلم
    deliverer_name = models.CharField(max_length=100, verbose_name='اسم المسلم')
    deliverer_job_number = models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمسلم')
    
    # بيانات المستلم
    receiver_name = models.CharField(max_length=100, verbose_name='اسم المستلم')
    receiver_job_number = models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمستلم')
    
    statement = models.TextField(verbose_name='البيان')
    is_locked = models.BooleanField(default=False, verbose_name='مقفل للتعديل')
    created_by = models.ForeignKey(CustomUser, on_delete=models.PROTECT, verbose_name='أنشئ بواسطة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    
    def save(self, *args, **kwargs):
        if self.pk:
            self.is_locked = True
        super().save(*args, **kwargs)
    
    # الحقول التي تُعبأ تلقائياً من عملية الوارد
    @property
    def incoming_date(self):
        return self.incoming_operation.operation_date
    
    @property
    def storage(self):
        return self.incoming_operation.storage
    
    @property
    def supplier(self):
        return self.incoming_operation.supplier
    
    @property
    def station(self):
        return self.incoming_operation.station
    
    def __str__(self):
        return f"مرتجع وارد {self.paper_number} - {self.incoming_operation.paper_number}"
    
    class Meta:
        verbose_name = 'مرتجع وارد'
        verbose_name_plural = 'مرتجعات الوارد'

class IncomingReturnItem(models.Model):
    incoming_return = models.ForeignKey(IncomingReturn, on_delete=models.CASCADE, related_name='items', verbose_name='مرتجع الوارد')
    category = models.ForeignKey(Category, on_delete=models.PROTECT, verbose_name='الصنف')
    returned_quantity = models.DecimalField(max_digits=15, decimal_places=3, verbose_name='الكمية المرتجعة')
    expected_return_date = models.DateField(verbose_name='تاريخ الرد المفترض')
    actual_return_date = models.DateField(blank=True, null=True, verbose_name='تاريخ الرد الفعلي')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    
    def clean(self):
        # التحقق من أن الكمية المرتجعة لا تتجاوز الكمية الأصلية في عملية الوارد
        try:
            original_item = IncomingOperationItem.objects.get(
                incoming_operation=self.incoming_return.incoming_operation,
                category=self.category
            )
            
            # حساب إجمالي الكميات المرتجعة سابقاً لهذا الصنف
            previous_returns = IncomingReturnItem.objects.filter(
                incoming_return__incoming_operation=self.incoming_return.incoming_operation,
                category=self.category
            ).exclude(id=self.id if self.id else None)
            
            total_previous_returns = sum(item.returned_quantity for item in previous_returns)
            
            if (total_previous_returns + self.returned_quantity) > original_item.imported_quantity:
                raise ValidationError(
                    f'إجمالي الكميات المرتجعة ({total_previous_returns + self.returned_quantity}) '
                    f'يتجاوز الكمية الأصلية ({original_item.imported_quantity})'
                )
        except IncomingOperationItem.DoesNotExist:
            raise ValidationError(f'الصنف {self.category.name} غير موجود في عملية الوارد المحددة')
    
    def save(self, *args, **kwargs):
        # التحقق من قفل العملية
        if self.incoming_return.is_locked and self.pk:
            raise ValidationError("لا يمكن تعديل هذه العملية لأنها مقفلة")
        
        self.clean()
        super().save(*args, **kwargs)
        
        # خصم الكمية المرتجعة من المخزن
        try:
            storage_item = StorageItem.objects.get(
                storage=self.incoming_return.storage,
                category=self.category
            )
            storage_item.current_quantity -= self.returned_quantity
            storage_item.save()
        except StorageItem.DoesNotExist:
            pass  # في حالة عدم وجود الصنف في المخزن
    
    def __str__(self):
        return f"{self.category.name} - {self.returned_quantity}"
    
    class Meta:
        verbose_name = 'صنف مرتجع وارد'
        verbose_name_plural = 'أصناف مرتجع الوارد'

class IncomingReturnFile(models.Model):
    incoming_return = models.ForeignKey(IncomingReturn, on_delete=models.CASCADE, related_name='files', verbose_name='مرتجع الوارد')
    file = models.FileField(upload_to='incoming_returns/', verbose_name='الملف')
    file_name = models.CharField(max_length=255, verbose_name='اسم الملف')
    uploaded_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')
    
    def __str__(self):
        return self.file_name
    
    class Meta:
        verbose_name = 'ملف مرفق لمرتجع الوارد'
        verbose_name_plural = 'الملفات المرفقة لمرتجع الوارد'

# نماذج مرتجع الصادر
class OutgoingReturn(models.Model):
    outgoing_operation = models.ForeignKey(OutgoingOperation, on_delete=models.PROTECT, verbose_name='عملية الصادر المراد الإرجاع منها')
    return_date = models.DateTimeField(verbose_name='تاريخ المرتجع')
    paper_number = models.CharField(max_length=50, verbose_name='الرقم الورقي')
    
    # بيانات المسلم
    deliverer_name = models.CharField(max_length=100, verbose_name='اسم المسلم')
    deliverer_job_number = models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمسلم')
    
    # بيانات المستلم
    receiver_name = models.CharField(max_length=100, verbose_name='اسم المستلم')
    receiver_job_number = models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمستلم')
    
    statement = models.TextField(verbose_name='البيان')
    is_locked = models.BooleanField(default=False, verbose_name='مقفل للتعديل')
    created_by = models.ForeignKey(CustomUser, on_delete=models.PROTECT, verbose_name='أنشئ بواسطة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    
    def save(self, *args, **kwargs):
        if self.pk:
            self.is_locked = True
        super().save(*args, **kwargs)
    
    # الحقول التي تُعبأ تلقائياً من عملية الصادر
    @property
    def outgoing_date(self):
        return self.outgoing_operation.operation_date
    
    @property
    def storage(self):
        return self.outgoing_operation.storage
    
    @property
    def beneficiary(self):
        return self.outgoing_operation.beneficiary
    
    def __str__(self):
        return f"مرتجع صادر {self.paper_number} - {self.outgoing_operation.paper_number}"
    
    class Meta:
        verbose_name = 'مرتجع صادر'
        verbose_name_plural = 'مرتجعات الصادر'

class OutgoingReturnItem(models.Model):
    outgoing_return = models.ForeignKey(OutgoingReturn, on_delete=models.CASCADE, related_name='items', verbose_name='مرتجع الصادر')
    category = models.ForeignKey(Category, on_delete=models.PROTECT, verbose_name='الصنف')
    returned_quantity = models.DecimalField(max_digits=15, decimal_places=3, verbose_name='الكمية المرتجعة')
    expected_return_date = models.DateField(verbose_name='تاريخ الرد المفترض')
    actual_return_date = models.DateField(blank=True, null=True, verbose_name='تاريخ الرد الفعلي')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    
    def clean(self):
        # التحقق من أن الكمية المرتجعة لا تتجاوز الكمية الأصلية في عملية الصادر
        try:
            original_item = OutgoingOperationItem.objects.get(
                outgoing_operation=self.outgoing_return.outgoing_operation,
                category=self.category
            )
            
            # حساب إجمالي الكميات المرتجعة سابقاً لهذا الصنف
            previous_returns = OutgoingReturnItem.objects.filter(
                outgoing_return__outgoing_operation=self.outgoing_return.outgoing_operation,
                category=self.category
            ).exclude(id=self.id if self.id else None)
            
            total_previous_returns = sum(item.returned_quantity for item in previous_returns)
            
            if (total_previous_returns + self.returned_quantity) > original_item.exported_quantity:
                raise ValidationError(
                    f'إجمالي الكميات المرتجعة ({total_previous_returns + self.returned_quantity}) '
                    f'يتجاوز الكمية الأصلية ({original_item.exported_quantity})'
                )
        except OutgoingOperationItem.DoesNotExist:
            raise ValidationError(f'الصنف {self.category.name} غير موجود في عملية الصادر المحددة')
    
    def save(self, *args, **kwargs):
        # التحقق من قفل العملية
        if self.outgoing_return.is_locked and self.pk:
            raise ValidationError("لا يمكن تعديل هذه العملية لأنها مقفلة")
        
        self.clean()
        super().save(*args, **kwargs)
        
        # إضافة الكمية المرتجعة إلى المخزن
        storage_item, created = StorageItem.objects.get_or_create(
            storage=self.outgoing_return.storage,
            category=self.category,
            defaults={
                'unit_of_measure': 'liter',  # وحدة افتراضية
                'opening_balance': 0,
                'current_quantity': 0
            }
        )
        storage_item.current_quantity += self.returned_quantity
        storage_item.save()
    
    def __str__(self):
        return f"{self.category.name} - {self.returned_quantity}"
    
    class Meta:
        verbose_name = 'صنف مرتجع صادر'
        verbose_name_plural = 'أصناف مرتجع الصادر'

class OutgoingReturnFile(models.Model):
    outgoing_return = models.ForeignKey(OutgoingReturn, on_delete=models.CASCADE, related_name='files', verbose_name='مرتجع الصادر')
    file = models.FileField(upload_to='outgoing_returns/', verbose_name='الملف')
    file_name = models.CharField(max_length=255, verbose_name='اسم الملف')
    uploaded_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')
    
    def __str__(self):
        return self.file_name
    
    class Meta:
        verbose_name = 'ملف مرفق لمرتجع الصادر'
        verbose_name_plural = 'الملفات المرفقة لمرتجع الصادر'

# نماذج عمليات التلف
class DamageOperation(models.Model):
    storage = models.ForeignKey(Storage, on_delete=models.PROTECT, verbose_name='المخزن')
    damage_date = models.DateTimeField(verbose_name='تاريخ التلف')
    paper_number = models.CharField(max_length=50, verbose_name='الرقم الورقي')
    
    # بيانات المسلم
    deliverer_name = models.CharField(max_length=100, verbose_name='اسم المسلم')
    deliverer_job_number = models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمسلم')
    
    # بيانات المستلم
    receiver_name = models.CharField(max_length=100, verbose_name='اسم المستلم')
    receiver_job_number = models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمستلم')
    
    statement = models.TextField(verbose_name='البيان')
    is_locked = models.BooleanField(default=False, verbose_name='مقفل للتعديل')
    created_by = models.ForeignKey(CustomUser, on_delete=models.PROTECT, verbose_name='أنشئ بواسطة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    
    def save(self, *args, **kwargs):
        if self.pk:
            self.is_locked = True
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"تلف {self.paper_number} - {self.storage.name}"
    
    class Meta:
        verbose_name = 'عملية تلف'
        verbose_name_plural = 'عمليات التلف'

class DamageOperationItem(models.Model):
    DAMAGE_REASONS = [
        ('expired', 'منتهي الصلاحية'),
        ('contaminated', 'ملوث'),
        ('leaked', 'تسرب'),
        ('fire', 'حريق'),
        ('accident', 'حادث'),
        ('quality_issue', 'مشكلة في الجودة'),
        ('other', 'أخرى'),
    ]
    
    damage_operation = models.ForeignKey(DamageOperation, on_delete=models.CASCADE, related_name='items', verbose_name='عملية التلف')
    category = models.ForeignKey(Category, on_delete=models.PROTECT, verbose_name='الصنف')
    damaged_quantity = models.DecimalField(max_digits=15, decimal_places=3, verbose_name='الكمية التالفة')
    reason = models.CharField(max_length=50, choices=DAMAGE_REASONS, verbose_name='السبب')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    
    def clean(self):
        # التحقق من توفر الكمية في المخزن
        try:
            storage_item = StorageItem.objects.get(
                storage=self.damage_operation.storage,
                category=self.category
            )
            if storage_item.current_quantity < self.damaged_quantity:
                raise ValidationError(f'الكمية المطلوبة ({self.damaged_quantity}) أكبر من الكمية المتوفرة ({storage_item.current_quantity})')
        except StorageItem.DoesNotExist:
            raise ValidationError(f'الصنف {self.category.name} غير موجود في المخزن {self.damage_operation.storage.name}')
    
    def save(self, *args, **kwargs):
        # التحقق من قفل العملية
        if self.damage_operation.is_locked and self.pk:
            raise ValidationError("لا يمكن تعديل هذه العملية لأنها مقفلة")
        
        self.clean()
        super().save(*args, **kwargs)
        # خصم الكمية التالفة من المخزن
        storage_item = StorageItem.objects.get(
            storage=self.damage_operation.storage,
            category=self.category
        )
        storage_item.current_quantity -= self.damaged_quantity
        storage_item.save()
    
    def __str__(self):
        return f"{self.category.name} - {self.damaged_quantity} ({self.get_reason_display()})"
    
    class Meta:
        verbose_name = 'صنف تالف'
        verbose_name_plural = 'أصناف التلف'

class DamageOperationFile(models.Model):
    damage_operation = models.ForeignKey(DamageOperation, on_delete=models.CASCADE, related_name='files', verbose_name='عملية التلف')
    file = models.FileField(upload_to='damage_operations/', verbose_name='الملف')
    file_name = models.CharField(max_length=255, verbose_name='اسم الملف')
    uploaded_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')
    
    def __str__(self):
        return self.file_name
    
    class Meta:
        verbose_name = 'ملف مرفق للتلف'
        verbose_name_plural = 'الملفات المرفقة للتلف'

# نماذج عمليات التحويل المخزني
class StorageTransfer(models.Model):
    from_storage = models.ForeignKey(Storage, on_delete=models.PROTECT, related_name='transfers_from', verbose_name='المخزن المحول منه')
    to_storage = models.ForeignKey(Storage, on_delete=models.PROTECT, related_name='transfers_to', verbose_name='المخزن المحول إليه')
    transfer_date = models.DateTimeField(verbose_name='تاريخ النقل')
    paper_number = models.CharField(max_length=50, verbose_name='الرقم الورقي')
    
    # بيانات المسلم
    deliverer_name = models.CharField(max_length=100, verbose_name='اسم المسلم')
    deliverer_job_number = models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمسلم')
    
    # بيانات المستلم
    receiver_name = models.CharField(max_length=100, verbose_name='اسم المستلم')
    receiver_job_number = models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمستلم')
    
    statement = models.TextField(verbose_name='البيان')
    is_locked = models.BooleanField(default=False, verbose_name='مقفل للتعديل')
    created_by = models.ForeignKey(CustomUser, on_delete=models.PROTECT, verbose_name='أنشئ بواسطة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    
    def clean(self):
        # التحقق من أن المخزن المصدر مختلف عن المخزن الهدف
        if self.from_storage == self.to_storage:
            raise ValidationError('لا يمكن التحويل من المخزن إلى نفسه')
    
    def save(self, *args, **kwargs):
        self.clean()
        if self.pk:
            self.is_locked = True
        super().save(*args, **kwargs)
    
    def __str__(self):
        return f"تحويل {self.paper_number} - من {self.from_storage.name} إلى {self.to_storage.name}"
    
    class Meta:
        verbose_name = 'عملية تحويل مخزني'
        verbose_name_plural = 'عمليات التحويل المخزني'

class StorageTransferItem(models.Model):
    TRANSFER_REASONS = [
        ('rebalancing', 'إعادة توزيع'),
        ('maintenance', 'صيانة المخزن'),
        ('capacity', 'سعة المخزن'),
        ('location', 'قرب الموقع'),
        ('emergency', 'حالة طوارئ'),
        ('optimization', 'تحسين التشغيل'),
        ('other', 'أخرى'),
    ]
    
    storage_transfer = models.ForeignKey(StorageTransfer, on_delete=models.CASCADE, related_name='items', verbose_name='عملية التحويل')
    category = models.ForeignKey(Category, on_delete=models.PROTECT, verbose_name='الصنف')
    transferred_quantity = models.DecimalField(max_digits=15, decimal_places=3, verbose_name='الكمية المحولة')
    reason = models.CharField(max_length=50, choices=TRANSFER_REASONS, verbose_name='السبب')
    notes = models.TextField(blank=True, null=True, verbose_name='ملاحظات')
    
    def clean(self):
        # التحقق من توفر الكمية في المخزن المصدر
        try:
            source_storage_item = StorageItem.objects.get(
                storage=self.storage_transfer.from_storage,
                category=self.category
            )
            if source_storage_item.current_quantity < self.transferred_quantity:
                raise ValidationError(
                    f'الكمية المطلوبة ({self.transferred_quantity}) أكبر من الكمية المتوفرة '
                    f'في المخزن المصدر ({source_storage_item.current_quantity})'
                )
        except StorageItem.DoesNotExist:
            raise ValidationError(f'الصنف {self.category.name} غير موجود في المخزن المصدر {self.storage_transfer.from_storage.name}')
    
    def save(self, *args, **kwargs):
        # التحقق من قفل العملية
        if self.storage_transfer.is_locked and self.pk:
            raise ValidationError("لا يمكن تعديل هذه العملية لأنها مقفلة")
        
        self.clean()
        super().save(*args, **kwargs)
        
        # خصم الكمية من المخزن المصدر
        source_storage_item = StorageItem.objects.get(
            storage=self.storage_transfer.from_storage,
            category=self.category
        )
        source_storage_item.current_quantity -= self.transferred_quantity
        source_storage_item.save()
        
        # إضافة الكمية إلى المخزن الهدف
        target_storage_item, created = StorageItem.objects.get_or_create(
            storage=self.storage_transfer.to_storage,
            category=self.category,
            defaults={
                'unit_of_measure': source_storage_item.unit_of_measure,
                'opening_balance': 0,
                'current_quantity': 0
            }
        )
        target_storage_item.current_quantity += self.transferred_quantity
        target_storage_item.save()
    
    def __str__(self):
        return f"{self.category.name} - {self.transferred_quantity} ({self.get_reason_display()})"
    
    class Meta:
        verbose_name = 'صنف محول'
        verbose_name_plural = 'أصناف التحويل'

class StorageTransferFile(models.Model):
    storage_transfer = models.ForeignKey(StorageTransfer, on_delete=models.CASCADE, related_name='files', verbose_name='عملية التحويل')
    file = models.FileField(upload_to='storage_transfers/', verbose_name='الملف')
    file_name = models.CharField(max_length=255, verbose_name='اسم الملف')
    uploaded_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')
    
    def __str__(self):
        return self.file_name
    
    class Meta:
        verbose_name = 'ملف مرفق للتحويل'
        verbose_name_plural = 'الملفات المرفقة للتحويل'
