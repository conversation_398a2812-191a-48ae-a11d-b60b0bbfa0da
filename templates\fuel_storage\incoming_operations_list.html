{% extends 'base.html' %}

{% block title %}عمليات الوارد - نظام إدارة مخازن المحروقات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">عمليات الوارد</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="/admin/fuel_storage/incomingoperation/add/" class="btn btn-sm btn-outline-secondary">
            <i class="fas fa-plus me-1"></i>
            إضافة عملية وارد جديدة
        </a>
    </div>
</div>

{% if operations %}
    <div class="table-responsive">
        <table class="table table-striped table-sm">
            <thead>
                <tr>
                    <th>الرقم الورقي</th>
                    <th>المخزن</th>
                    <th>المورد</th>
                    <th>المحطة</th>
                    <th>تاريخ العملية</th>
                    <th>المسلم</th>
                    <th>المستلم</th>
                    <th>أنشئ بواسطة</th>
                    <th>تاريخ الإنشاء</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for operation in operations %}
                <tr>
                    <td>
                        <span class="badge bg-success">{{ operation.paper_number }}</span>
                    </td>
                    <td>{{ operation.storage.name }}</td>
                    <td>{{ operation.supplier.full_name }}</td>
                    <td>{{ operation.station.name }}</td>
                    <td class="date-display">{{ operation.operation_date|date:"Y-m-d H:i" }}</td>
                    <td>
                        <div>{{ operation.deliverer_name }}</div>
                        <small class="text-muted">{{ operation.deliverer_job_number }}</small>
                    </td>
                    <td>
                        <div>{{ operation.receiver_name }}</div>
                        <small class="text-muted">{{ operation.receiver_job_number }}</small>
                    </td>
                    <td>{{ operation.created_by.full_name|default:operation.created_by.username }}</td>
                    <td class="date-display">{{ operation.created_at|date:"Y-m-d H:i" }}</td>
                    <td>
                        <a href="/admin/fuel_storage/incomingoperation/{{ operation.id }}/change/" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-edit"></i>
                        </a>
                        {% if operation.is_locked %}
                            <i class="fas fa-lock text-danger ms-1" title="مقفل للتعديل"></i>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
{% else %}
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        لا توجد عمليات وارد مسجلة حتى الآن.
    </div>
{% endif %}
{% endblock %}
