# -*- coding: utf-8 -*-
"""
إعدادات الخطوط للنظام
"""

import os
import platform
from django.conf import settings


def get_available_arabic_fonts():
    """الحصول على قائمة بالخطوط العربية المتاحة في النظام"""
    system = platform.system()
    fonts = []
    
    if system == "Windows":
        # مسارات الخطوط في Windows
        windows_fonts = [
            ("Arial", "C:/Windows/Fonts/arial.ttf"),
            ("Tahoma", "C:/Windows/Fonts/tahoma.ttf"),
            ("Calibri", "C:/Windows/Fonts/calibri.ttf"),
            ("Times New Roman", "C:/Windows/Fonts/times.ttf"),
            ("Segoe UI", "C:/Windows/Fonts/segoeui.ttf"),
        ]
        
        for name, path in windows_fonts:
            if os.path.exists(path):
                fonts.append((name, path))
                
    elif system == "Darwin":  # macOS
        mac_fonts = [
            ("Arial", "/System/Library/Fonts/Arial.ttf"),
            ("Times", "/System/Library/Fonts/Times.ttc"),
            ("Helvetica", "/System/Library/Fonts/Helvetica.ttc"),
        ]
        
        for name, path in mac_fonts:
            if os.path.exists(path):
                fonts.append((name, path))
                
    else:  # Linux
        linux_fonts = [
            ("DejaVu Sans", "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"),
            ("Liberation Sans", "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf"),
            ("Ubuntu", "/usr/share/fonts/truetype/ubuntu/Ubuntu-R.ttf"),
        ]
        
        for name, path in linux_fonts:
            if os.path.exists(path):
                fonts.append((name, path))
    
    return fonts


def get_best_arabic_font():
    """الحصول على أفضل خط عربي متاح"""
    # محاولة استخدام خط مخصص أولاً
    custom_font_path = os.path.join(
        getattr(settings, 'STATIC_ROOT', 'static'), 
        'fonts', 
        'NotoSansArabic-Regular.ttf'
    )
    
    if os.path.exists(custom_font_path):
        return ("Noto Sans Arabic", custom_font_path)
    
    # استخدام خطوط النظام
    available_fonts = get_available_arabic_fonts()
    
    # ترتيب الأولوية للخطوط
    preferred_fonts = ["Tahoma", "Arial", "Calibri", "Segoe UI", "DejaVu Sans"]
    
    for preferred in preferred_fonts:
        for name, path in available_fonts:
            if preferred.lower() in name.lower():
                return (name, path)
    
    # إرجاع أول خط متاح
    if available_fonts:
        return available_fonts[0]
    
    return None


# إعدادات افتراضية للخطوط
FONT_CONFIG = {
    'default_size': 11,
    'title_size': 16,
    'heading_size': 14,
    'small_size': 9,
    'line_height_factor': 1.3,
}


def get_font_config():
    """الحصول على إعدادات الخطوط"""
    return FONT_CONFIG.copy()
