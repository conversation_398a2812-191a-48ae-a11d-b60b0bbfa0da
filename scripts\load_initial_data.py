#!/usr/bin/env python
"""
سكريبت لتحميل البيانات الأولية للنظام
"""
import os
import sys
import django
import sqlite3
from pathlib import Path

# إعداد Django
BASE_DIR = Path(__file__).resolve().parent.parent
sys.path.append(str(BASE_DIR))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fuel_storage_system.settings')
django.setup()

def load_initial_data():
    """تحميل البيانات الأولية من ملف SQL"""
    try:
        # الاتصال بقاعدة البيانات
        db_path = BASE_DIR / 'db.sqlite3'
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # قراءة ملف SQL
        sql_file = BASE_DIR / 'scripts' / 'initial_data.sql'
        with open(sql_file, 'r', encoding='utf-8') as f:
            sql_script = f.read()
        
        # تنفيذ الاستعلامات
        cursor.executescript(sql_script)
        conn.commit()
        
        print("✅ تم تحميل البيانات الأولية بنجاح!")
        print("📊 تم إضافة:")
        print("   - 8 أصناف أساسية")
        print("   - 5 محطات")
        print("   - 5 موردين")
        print("   - 6 مستفيدين")
        print("   - 5 مخازن")
        
    except Exception as e:
        print(f"❌ خطأ في تحميل البيانات: {e}")
    finally:
        if conn:
            conn.close()

if __name__ == '__main__':
    load_initial_data()
