/* تنسيقات مخصصة لنظام إدارة مخازن المحروقات */

/* متغيرات CSS للألوان والقيم */
:root {
  --primary-gradient: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  --success-gradient: linear-gradient(135deg, #059669 0%, #047857 100%);
  --warning-gradient: linear-gradient(135deg, #d97706 0%, #b45309 100%);
  --danger-gradient: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  --info-gradient: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
  --glass-bg: rgba(255, 255, 255, 0.95);
  --glass-border: rgba(255, 255, 255, 0.2);
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --border-radius-sm: 0.375rem;
  --border-radius-md: 0.5rem;
  --border-radius-lg: 0.75rem;
  --border-radius-xl: 1rem;
  --transition-fast: all 0.15s ease;
  --transition-normal: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تحسين الخطوط العربية */
body {
  font-family: 'Cairo', 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
  direction: rtl;
  text-align: right;
  font-weight: 400;
  line-height: 1.6;
}

/* تنسيق الجداول المحسن */
.table {
  border-collapse: separate;
  border-spacing: 0;
  margin-bottom: 1.5rem;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
}

.table th {
  background: var(--primary-gradient);
  color: white;
  border: none;
  font-weight: 600;
  padding: 1rem;
  text-align: center;
  position: relative;
}

.table th::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: rgba(255, 255, 255, 0.3);
}

.table td {
  vertical-align: middle;
  padding: 0.875rem 1rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: var(--transition-fast);
}

.table tbody tr:hover {
  background-color: rgba(37, 99, 235, 0.05);
  transform: scale(1.01);
}

.table tbody tr:last-child td {
  border-bottom: none;
}

/* تنسيق البطاقات المحسن */
.card {
  border: none;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  margin-bottom: 1.5rem;
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  transition: var(--transition-normal);
  overflow: hidden;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.card-header {
  background: var(--primary-gradient);
  color: white;
  border: none;
  font-weight: 600;
  padding: 1.25rem;
  position: relative;
}

.card-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: rgba(255, 255, 255, 0.3);
}

.card-body {
  padding: 1.5rem;
}

.card-footer {
  background: rgba(248, 250, 252, 0.8);
  border: none;
  padding: 1rem 1.5rem;
}

/* تنسيق الأزرار المحسن */
.btn {
  border-radius: var(--border-radius-md);
  font-weight: 500;
  padding: 0.625rem 1.25rem;
  transition: var(--transition-normal);
  border: none;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: var(--transition-normal);
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: var(--primary-gradient);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4);
  color: white;
}

.btn-success {
  background: var(--success-gradient);
  color: white;
}

.btn-success:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(5, 150, 105, 0.4);
  color: white;
}

.btn-warning {
  background: var(--warning-gradient);
  color: white;
}

.btn-warning:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(217, 119, 6, 0.4);
  color: white;
}

.btn-danger {
  background: var(--danger-gradient);
  color: white;
}

.btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.4);
  color: white;
}

.btn-info {
  background: var(--info-gradient);
  color: white;
}

.btn-info:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(8, 145, 178, 0.4);
  color: white;
}

/* تنسيقات إضافية للبطاقات الإحصائية */
.stat-card {
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
}

.stat-card:hover {
  transform: translateY(-6px) scale(1.02);
  box-shadow: var(--shadow-xl);
}

.stat-card .card-body {
  position: relative;
  z-index: 1;
}

.stat-card .fa-2x {
  opacity: 0.1;
  position: absolute;
  top: 50%;
  right: 1rem;
  transform: translateY(-50%);
  font-size: 3rem !important;
}

/* تنسيق الأرقام والإحصائيات */
.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1;
}

.stat-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* تنسيق الحدود الملونة للبطاقات */
.border-right-primary {
  border-right: 4px solid #2563eb !important;
  position: relative;
}

.border-right-primary::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
  background: var(--primary-gradient);
  border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
}

.border-right-success {
  border-right: 4px solid #059669 !important;
  position: relative;
}

.border-right-success::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
  background: var(--success-gradient);
  border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
}

.border-right-info {
  border-right: 4px solid #0891b2 !important;
  position: relative;
}

.border-right-info::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
  background: var(--info-gradient);
  border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
}

.border-right-warning {
  border-right: 4px solid #d97706 !important;
  position: relative;
}

.border-right-warning::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
  background: var(--warning-gradient);
  border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
}

.border-right-danger {
  border-right: 4px solid #dc2626 !important;
  position: relative;
}

.border-right-danger::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
  background: var(--danger-gradient);
  border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
}

.border-right-secondary {
  border-right: 4px solid #64748b !important;
  position: relative;
}

.border-right-secondary::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, #64748b 0%, #475569 100%);
  border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
}

/* تنسيق النماذج المحسن */
.form-control {
  border-radius: var(--border-radius-md);
  border: 2px solid #e2e8f0;
  padding: 0.75rem 1rem;
  transition: var(--transition-normal);
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
}

.form-control:focus {
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  background: white;
}

.form-select {
  border-radius: var(--border-radius-md);
  border: 2px solid #e2e8f0;
  padding: 0.75rem 1rem;
  transition: var(--transition-normal);
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
}

.form-select:focus {
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-label {
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

/* تنسيق التنبيهات المحسن */
.alert {
  border-radius: var(--border-radius-lg);
  border: none;
  padding: 1.25rem;
  margin-bottom: 1.5rem;
  box-shadow: var(--shadow-md);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.alert::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.alert-info {
  background: rgba(8, 145, 178, 0.1);
  color: #0e7490;
  border: 1px solid rgba(8, 145, 178, 0.2);
}

.alert-info::before {
  background: var(--info-gradient);
}

.alert-success {
  background: rgba(5, 150, 105, 0.1);
  color: #047857;
  border: 1px solid rgba(5, 150, 105, 0.2);
}

.alert-success::before {
  background: var(--success-gradient);
}

.alert-warning {
  background: rgba(217, 119, 6, 0.1);
  color: #b45309;
  border: 1px solid rgba(217, 119, 6, 0.2);
}

.alert-warning::before {
  background: var(--warning-gradient);
}

.alert-danger {
  background: rgba(220, 38, 38, 0.1);
  color: #b91c1c;
  border: 1px solid rgba(220, 38, 38, 0.2);
}

.alert-danger::before {
  background: var(--danger-gradient);
}

/* تنسيق الشارات المحسن */
.badge {
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: var(--border-radius-md);
  padding: 0.375rem 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: var(--shadow-sm);
  transition: var(--transition-fast);
}

.badge:hover {
  transform: scale(1.05);
}

.badge.bg-primary {
  background: var(--primary-gradient) !important;
}

.badge.bg-success {
  background: var(--success-gradient) !important;
}

.badge.bg-warning {
  background: var(--warning-gradient) !important;
}

.badge.bg-danger {
  background: var(--danger-gradient) !important;
}

.badge.bg-info {
  background: var(--info-gradient) !important;
}

/* تنسيق مؤشرات التحميل */
.loading-spinner {
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
  border: 3px solid rgba(37, 99, 235, 0.1);
  border-top: 3px solid #2563eb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* تنسيق التقدم */
.progress {
  height: 0.75rem;
  border-radius: var(--border-radius-md);
  background: rgba(0, 0, 0, 0.05);
  overflow: hidden;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.progress-bar {
  background: var(--primary-gradient);
  transition: width 0.6s ease;
  position: relative;
  overflow: hidden;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 1rem 1rem;
  animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
  0% { background-position: 1rem 0; }
  100% { background-position: 0 0; }
}

/* تنسيق الأيقونات */
.icon-wrapper {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background: var(--primary-gradient);
  color: white;
  box-shadow: var(--shadow-md);
  transition: var(--transition-normal);
}

.icon-wrapper:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-lg);
}

.icon-wrapper.success {
  background: var(--success-gradient);
}

.icon-wrapper.warning {
  background: var(--warning-gradient);
}

.icon-wrapper.danger {
  background: var(--danger-gradient);
}

.icon-wrapper.info {
  background: var(--info-gradient);
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.loading-content {
  text-align: center;
  color: white;
}

.loading-content .loading-spinner {
  width: 3rem;
  height: 3rem;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

.loading-text {
  font-size: 1.1rem;
  font-weight: 500;
}

/* Enhanced Toast Styles */
.toast {
  backdrop-filter: blur(10px);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
}

.toast .toast-body {
  padding: 1rem;
}

/* Modal Enhancements */
.modal-content {
  border: none;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  backdrop-filter: blur(10px);
}

.modal-header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
}

/* Progress Bar Enhancements */
.progress-wrapper {
  position: relative;
}

.progress-label {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.progress {
  background: rgba(0, 0, 0, 0.1);
  border-radius: var(--border-radius-md);
  overflow: hidden;
}

.progress-bar {
  background: var(--primary-gradient);
  transition: width 0.6s ease;
}

.progress-text {
  text-align: center;
  font-weight: 500;
}

/* Tooltip Enhancements */
.tooltip {
  font-size: 0.875rem;
}

.tooltip-inner {
  background: var(--dark-color);
  border-radius: var(--border-radius-md);
  padding: 0.5rem 0.75rem;
  box-shadow: var(--shadow-md);
}

/* Popover Enhancements */
.popover {
  border: none;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  backdrop-filter: blur(10px);
}

.popover-header {
  background: var(--primary-gradient);
  color: white;
  border: none;
  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
  font-weight: 600;
}

.popover-body {
  padding: 1rem;
}

/* Dropdown Enhancements */
.dropdown-menu {
  border: none;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  backdrop-filter: blur(10px);
  background: var(--glass-bg);
}

.dropdown-item {
  padding: 0.75rem 1rem;
  transition: var(--transition-fast);
  border-radius: var(--border-radius-sm);
  margin: 0.125rem 0.5rem;
}

.dropdown-item:hover {
  background: rgba(37, 99, 235, 0.1);
  color: #2563eb;
}

.dropdown-divider {
  margin: 0.5rem 0;
  border-color: rgba(0, 0, 0, 0.1);
}

/* Enhanced Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.5s ease-out;
}

.animate-fade-in-scale {
  animation: fadeInScale 0.3s ease-out;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

/* Responsive Enhancements */
@media (max-width: 576px) {
  .modal-dialog {
    margin: 1rem;
  }

  .toast-container {
    left: 1rem;
    right: 1rem;
  }

  .loading-content .loading-spinner {
    width: 2rem;
    height: 2rem;
  }

  .loading-text {
    font-size: 1rem;
  }
}

/* تحسين التجاوب */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    top: 56px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 56px);
    transition: left 0.3s ease;
    z-index: 1000;
  }

  .sidebar.show {
    left: 0;
  }

  .table-responsive {
    font-size: 0.875rem;
  }
}

/* تنسيق الطباعة */
@media print {
  .sidebar,
  .navbar,
  .btn,
  .no-print {
    display: none !important;
  }

  .main-content {
    margin: 0 !important;
    padding: 0 !important;
  }

  .card {
    border: 1px solid #000 !important;
    box-shadow: none !important;
  }

  .table {
    border-collapse: collapse !important;
  }

  .table th,
  .table td {
    border: 1px solid #000 !important;
    padding: 0.5rem !important;
  }
}

/* تنسيق خاص بالتقارير */
.report-header {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #dee2e6;
}

.report-section {
  margin-bottom: 2rem;
}

.report-section h3 {
  color: #495057;
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 0.5rem;
  margin-bottom: 1rem;
}

/* تحسين عرض الأرقام */
.number-display {
  font-family: "Courier New", monospace;
  font-weight: bold;
  text-align: left;
  direction: ltr;
}

/* تنسيق حالة المخزون */
.stock-status-high {
  color: #198754;
  font-weight: bold;
}

.stock-status-medium {
  color: #ffc107;
  font-weight: bold;
}

.stock-status-low {
  color: #dc3545;
  font-weight: bold;
}

/* تحسين عرض التواريخ */
.date-display {
  font-family: "Courier New", monospace;
  direction: ltr;
  text-align: left;
}

/* تنسيق أيقونات الحالة */
.status-icon {
  width: 1rem;
  height: 1rem;
  display: inline-block;
  margin-left: 0.5rem;
}

.status-active {
  color: #198754;
}

.status-inactive {
  color: #6c757d;
}

.status-locked {
  color: #dc3545;
}

/* تحسين عرض الملفات المرفقة */
.file-attachment {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  margin: 0.125rem;
  text-decoration: none;
  color: #495057;
}

.file-attachment:hover {
  background-color: #e9ecef;
  color: #495057;
  text-decoration: none;
}

/* تنسيق مؤشرات التحميل */
.loading-spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #0d6efd;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* تحسين عرض الإحصائيات */
.stat-card {
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-number {
  font-size: 2rem;
  font-weight: bold;
  color: #495057;
}

.stat-label {
  font-size: 0.875rem;
  color: #6c757d;
  text-transform: uppercase;
  font-weight: 600;
}
