from django.http import HttpResponse
from django.template.loader import get_template
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill
from openpyxl.utils import get_column_letter
import io
from datetime import datetime

def generate_pdf_report(report_data, report_type):
    """إنشاء تقرير PDF"""
    buffer = io.BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=72, leftMargin=72, topMargin=72, bottomMargin=18)
    
    # إعداد الخطوط العربية (يتطلب ملف خط عربي)
    try:
        pdfmetrics.registerFont(TTFont('Arabic', 'static/fonts/NotoSansArabic-Regular.ttf'))
        arabic_style = ParagraphStyle('Arabic', fontName='Arabic', fontSize=12, alignment=2)  # RTL alignment
        title_style = ParagraphStyle('ArabicTitle', fontName='Arabic', fontSize=16, alignment=1, spaceAfter=20)
    except:
        # استخدام الخط الافتراضي في حالة عدم توفر الخط العربي
        arabic_style = ParagraphStyle('Arabic', fontName='Helvetica', fontSize=12, alignment=2)
        title_style = ParagraphStyle('ArabicTitle', fontName='Helvetica-Bold', fontSize=16, alignment=1, spaceAfter=20)
    
    story = []
    
    # العنوان
    title = Paragraph(report_data['title'], title_style)
    story.append(title)
    story.append(Spacer(1, 12))
    
    # معلومات التقرير
    if report_data.get('from_date') and report_data.get('to_date'):
        date_info = f"من تاريخ: {report_data['from_date']} إلى تاريخ: {report_data['to_date']}"
        story.append(Paragraph(date_info, arabic_style))
        story.append(Spacer(1, 12))
    
    # إنشاء المحتوى حسب نوع التقرير
    if report_type == 'storage_movement':
        story.extend(_create_storage_movement_content(report_data, arabic_style))
    elif report_type == 'storage_status':
        story.extend(_create_storage_status_content(report_data, arabic_style))
    
    # إنشاء PDF
    doc.build(story)
    buffer.seek(0)
    
    response = HttpResponse(buffer.getvalue(), content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="{report_type}_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.pdf"'
    
    return response

def generate_excel_report(report_data, report_type):
    """إنشاء تقرير Excel"""
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "التقرير"
    
    # تنسيق الخلايا
    header_font = Font(bold=True, size=14)
    title_font = Font(bold=True, size=16)
    normal_font = Font(size=12)
    
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    
    # العنوان
    ws['A1'] = report_data['title']
    ws['A1'].font = title_font
    ws['A1'].alignment = Alignment(horizontal='center')
    
    # معلومات التاريخ
    row = 3
    if report_data.get('from_date') and report_data.get('to_date'):
        ws[f'A{row}'] = f"من تاريخ: {report_data['from_date']} إلى تاريخ: {report_data['to_date']}"
        ws[f'A{row}'].font = normal_font
        row += 2
    
    # إنشاء المحتوى حسب نوع التقرير
    if report_type == 'storage_movement':
        _create_excel_storage_movement(ws, report_data, row, header_font, normal_font, header_fill)
    elif report_type == 'storage_status':
        _create_excel_storage_status(ws, report_data, row, header_font, normal_font, header_fill)
    
    # حفظ الملف
    buffer = io.BytesIO()
    wb.save(buffer)
    buffer.seek(0)
    
    response = HttpResponse(
        buffer.getvalue(),
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = f'attachment; filename="{report_type}_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'
    
    return response

# دوال مساعدة لإنشاء محتوى PDF
def _create_storage_movement_content(report_data, style):
    content = []
    
    # عمليات الوارد
    if report_data['data']['incoming_operations']:
        content.append(Paragraph("عمليات الوارد", style))
        content.append(Spacer(1, 6))
        
        data = [['الرقم الورقي', 'المورد', 'التاريخ', 'الكمية الإجمالية']]
        for op in report_data['data']['incoming_operations']:
            total_qty = sum(item.imported_quantity for item in op.items.all())
            data.append([op.paper_number, op.supplier.full_name, 
                        op.operation_date.strftime('%Y-%m-%d'), str(total_qty)])
        
        table = Table(data)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        content.append(table)
        content.append(Spacer(1, 12))
    
    return content

def _create_storage_status_content(report_data, style):
    content = []
    
    if report_data['items']:
        content.append(Paragraph("أصناف المخزن", style))
        content.append(Spacer(1, 6))
        
        data = [['الصنف', 'وحدة القياس', 'الكمية الحالية', 'الرصيد الافتتاحي']]
        for item in report_data['items']:
            data.append([
                item.category.name,
                item.get_unit_of_measure_display(),
                str(item.current_quantity),
                str(item.opening_balance)
            ])
        
        table = Table(data)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        content.append(table)
    
    return content

# دوال مساعدة لإنشاء محتوى Excel
def _create_excel_storage_movement(ws, report_data, start_row, header_font, normal_font, header_fill):
    row = start_row
    
    # عمليات الوارد
    if report_data['data']['incoming_operations']:
        ws[f'A{row}'] = "عمليات الوارد"
        ws[f'A{row}'].font = header_font
        row += 2
        
        # رؤوس الأعمدة
        headers = ['الرقم الورقي', 'المورد', 'التاريخ', 'الكمية الإجمالية']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=row, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
        
        row += 1
        
        # البيانات
        for op in report_data['data']['incoming_operations']:
            total_qty = sum(item.imported_quantity for item in op.items.all())
            ws.cell(row=row, column=1, value=op.paper_number).font = normal_font
            ws.cell(row=row, column=2, value=op.supplier.full_name).font = normal_font
            ws.cell(row=row, column=3, value=op.operation_date.strftime('%Y-%m-%d')).font = normal_font
            ws.cell(row=row, column=4, value=float(total_qty)).font = normal_font
            row += 1
        
        row += 2

def _create_excel_storage_status(ws, report_data, start_row, header_font, normal_font, header_fill):
    row = start_row
    
    if report_data['items']:
        ws[f'A{row}'] = "أصناف المخزن"
        ws[f'A{row}'].font = header_font
        row += 2
        
        # رؤوس الأعمدة
        headers = ['الصنف', 'وحدة القياس', 'الكمية الحالية', 'الرصيد الافتتاحي']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=row, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
        
        row += 1
        
        # البيانات
        for item in report_data['items']:
            ws.cell(row=row, column=1, value=item.category.name).font = normal_font
            ws.cell(row=row, column=2, value=item.get_unit_of_measure_display()).font = normal_font
            ws.cell(row=row, column=3, value=float(item.current_quantity)).font = normal_font
            ws.cell(row=row, column=4, value=float(item.opening_balance)).font = normal_font
            row += 1
