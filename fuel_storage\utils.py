from django.http import HttpResponse
from django.template.loader import get_template
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, PageBreak
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib.enums import TA_RIGHT, TA_CENTER, TA_LEFT
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill
from openpyxl.utils import get_column_letter
import io
from datetime import datetime
from .arabic_pdf_utils import (
    create_arabic_paragraph_styles,
    process_arabic_text,
    create_arabic_table_data,
    format_arabic_number,
    create_arabic_date_string
)

def generate_pdf_report(report_data, report_type):
    """إنشاء تقرير PDF مع دعم كامل للغة العربية"""
    buffer = io.BytesIO()

    # إعداد الصفحة مع هوامش مناسبة للنصوص العربية
    doc = SimpleDocTemplate(
        buffer,
        pagesize=A4,
        rightMargin=50,
        leftMargin=50,
        topMargin=50,
        bottomMargin=50,
        title=process_arabic_text(report_data.get('title', 'تقرير'))
    )

    # إنشاء أنماط الفقرات العربية
    styles = create_arabic_paragraph_styles()

    story = []

    # إضافة رأس الصفحة
    story.append(_create_pdf_header(report_data, styles))
    story.append(Spacer(1, 20))

    # العنوان الرئيسي
    title_text = process_arabic_text(report_data.get('title', 'تقرير'))
    title = Paragraph(title_text, styles['arabic_title'])
    story.append(title)
    story.append(Spacer(1, 15))

    # معلومات التقرير
    story.extend(_create_report_info_section(report_data, styles))
    story.append(Spacer(1, 20))

    # إنشاء المحتوى حسب نوع التقرير
    if report_type == 'storage_movement':
        story.extend(_create_storage_movement_content(report_data, styles))
    elif report_type == 'storage_status':
        story.extend(_create_storage_status_content(report_data, styles))
    elif report_type == 'category_movement':
        story.extend(_create_category_movement_content(report_data, styles))
    elif report_type == 'category_status':
        story.extend(_create_category_status_content(report_data, styles))
    elif report_type == 'beneficiary_movement':
        story.extend(_create_beneficiary_movement_content(report_data, styles))
    elif report_type == 'supplier_movement':
        story.extend(_create_supplier_movement_content(report_data, styles))

    # إضافة تذييل الصفحة
    story.append(Spacer(1, 30))
    story.extend(_create_pdf_footer(styles))

    # إنشاء PDF
    doc.build(story)
    buffer.seek(0)

    # إنشاء اسم ملف عربي
    filename = f"{report_type}_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"

    response = HttpResponse(buffer.getvalue(), content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="{filename}"'
    response['Content-Type'] = 'application/pdf; charset=utf-8'

    return response

def generate_excel_report(report_data, report_type):
    """إنشاء تقرير Excel"""
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "التقرير"
    
    # تنسيق الخلايا
    header_font = Font(bold=True, size=14)
    title_font = Font(bold=True, size=16)
    normal_font = Font(size=12)
    
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    
    # العنوان
    ws['A1'] = report_data['title']
    ws['A1'].font = title_font
    ws['A1'].alignment = Alignment(horizontal='center')
    
    # معلومات التاريخ
    row = 3
    if report_data.get('from_date') and report_data.get('to_date'):
        ws[f'A{row}'] = f"من تاريخ: {report_data['from_date']} إلى تاريخ: {report_data['to_date']}"
        ws[f'A{row}'].font = normal_font
        row += 2
    
    # إنشاء المحتوى حسب نوع التقرير
    if report_type == 'storage_movement':
        _create_excel_storage_movement(ws, report_data, row, header_font, normal_font, header_fill)
    elif report_type == 'storage_status':
        _create_excel_storage_status(ws, report_data, row, header_font, normal_font, header_fill)
    
    # حفظ الملف
    buffer = io.BytesIO()
    wb.save(buffer)
    buffer.seek(0)
    
    response = HttpResponse(
        buffer.getvalue(),
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = f'attachment; filename="{report_type}_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'
    
    return response

# دوال مساعدة لإنشاء محتوى PDF
def _create_storage_movement_content(report_data, styles):
    content = []

    # عمليات الوارد
    if report_data['data']['incoming_operations']:
        # عنوان قسم عمليات الوارد
        heading_text = process_arabic_text("عمليات الوارد")
        content.append(Paragraph(heading_text, styles['arabic_heading']))
        content.append(Spacer(1, 10))

        # إعداد بيانات الجدول
        headers = ['الرقم الورقي', 'المورد', 'التاريخ', 'الكمية الإجمالية']
        table_data = []

        for op in report_data['data']['incoming_operations']:
            total_qty = sum(item.imported_quantity for item in op.items.all())
            row = [
                op.paper_number,
                op.supplier.full_name,
                create_arabic_date_string(op.operation_date),
                format_arabic_number(total_qty)
            ]
            table_data.append(row)

        # إنشاء الجدول مع معالجة النصوص العربية
        processed_data = create_arabic_table_data(table_data, headers)

        table = Table(processed_data, colWidths=[80, 120, 100, 80])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2563eb')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Arabic-Bold'),
            ('FONTNAME', (0, 1), (-1, -1), 'Arabic'),
            ('FONTSIZE', (0, 0), (-1, 0), 11),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('TOPPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.HexColor('#f8fafc')),
            ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#e2e8f0')),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))
        content.append(table)
        content.append(Spacer(1, 15))

    return content

def _create_storage_status_content(report_data, styles):
    content = []

    if report_data['items']:
        # عنوان قسم أصناف المخزن
        heading_text = process_arabic_text("أصناف المخزن")
        content.append(Paragraph(heading_text, styles['arabic_heading']))
        content.append(Spacer(1, 10))

        # إعداد بيانات الجدول
        headers = ['الصنف', 'وحدة القياس', 'الكمية الحالية', 'الرصيد الافتتاحي']
        table_data = []

        for item in report_data['items']:
            row = [
                item.category.name,
                item.get_unit_of_measure_display(),
                format_arabic_number(item.current_quantity),
                format_arabic_number(item.opening_balance)
            ]
            table_data.append(row)

        # إنشاء الجدول مع معالجة النصوص العربية
        processed_data = create_arabic_table_data(table_data, headers)

        table = Table(processed_data, colWidths=[120, 80, 80, 80])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#059669')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Arabic-Bold'),
            ('FONTNAME', (0, 1), (-1, -1), 'Arabic'),
            ('FONTSIZE', (0, 0), (-1, 0), 11),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('TOPPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.HexColor('#f8fafc')),
            ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#e2e8f0')),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))
        content.append(table)
        content.append(Spacer(1, 15))

    return content


def _create_category_movement_content(report_data, styles):
    """إنشاء محتوى تقرير حركة الصنف"""
    content = []

    heading_text = process_arabic_text("حركة الصنف")
    content.append(Paragraph(heading_text, styles['arabic_heading']))
    content.append(Spacer(1, 10))

    # يمكن إضافة المزيد من التفاصيل هنا
    info_text = process_arabic_text("تقرير حركة الصنف قيد التطوير")
    content.append(Paragraph(info_text, styles['arabic_normal']))

    return content


def _create_category_status_content(report_data, styles):
    """إنشاء محتوى تقرير حالة الصنف"""
    content = []

    heading_text = process_arabic_text("حالة الصنف")
    content.append(Paragraph(heading_text, styles['arabic_heading']))
    content.append(Spacer(1, 10))

    info_text = process_arabic_text("تقرير حالة الصنف قيد التطوير")
    content.append(Paragraph(info_text, styles['arabic_normal']))

    return content


def _create_beneficiary_movement_content(report_data, styles):
    """إنشاء محتوى تقرير حركة المستفيد"""
    content = []

    heading_text = process_arabic_text("حركة المستفيد")
    content.append(Paragraph(heading_text, styles['arabic_heading']))
    content.append(Spacer(1, 10))

    info_text = process_arabic_text("تقرير حركة المستفيد قيد التطوير")
    content.append(Paragraph(info_text, styles['arabic_normal']))

    return content


def _create_supplier_movement_content(report_data, styles):
    """إنشاء محتوى تقرير حركة المورد"""
    content = []

    heading_text = process_arabic_text("حركة المورد")
    content.append(Paragraph(heading_text, styles['arabic_heading']))
    content.append(Spacer(1, 10))

    info_text = process_arabic_text("تقرير حركة المورد قيد التطوير")
    content.append(Paragraph(info_text, styles['arabic_normal']))

    return content

# دوال مساعدة لإنشاء محتوى Excel
def _create_excel_storage_movement(ws, report_data, start_row, header_font, normal_font, header_fill):
    row = start_row
    
    # عمليات الوارد
    if report_data['data']['incoming_operations']:
        ws[f'A{row}'] = "عمليات الوارد"
        ws[f'A{row}'].font = header_font
        row += 2
        
        # رؤوس الأعمدة
        headers = ['الرقم الورقي', 'المورد', 'التاريخ', 'الكمية الإجمالية']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=row, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
        
        row += 1
        
        # البيانات
        for op in report_data['data']['incoming_operations']:
            total_qty = sum(item.imported_quantity for item in op.items.all())
            ws.cell(row=row, column=1, value=op.paper_number).font = normal_font
            ws.cell(row=row, column=2, value=op.supplier.full_name).font = normal_font
            ws.cell(row=row, column=3, value=op.operation_date.strftime('%Y-%m-%d')).font = normal_font
            ws.cell(row=row, column=4, value=float(total_qty)).font = normal_font
            row += 1
        
        row += 2

def _create_excel_storage_status(ws, report_data, start_row, header_font, normal_font, header_fill):
    row = start_row
    
    if report_data['items']:
        ws[f'A{row}'] = "أصناف المخزن"
        ws[f'A{row}'].font = header_font
        row += 2
        
        # رؤوس الأعمدة
        headers = ['الصنف', 'وحدة القياس', 'الكمية الحالية', 'الرصيد الافتتاحي']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=row, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
        
        row += 1
        
        # البيانات
        for item in report_data['items']:
            ws.cell(row=row, column=1, value=item.category.name).font = normal_font
            ws.cell(row=row, column=2, value=item.get_unit_of_measure_display()).font = normal_font
            ws.cell(row=row, column=3, value=float(item.current_quantity)).font = normal_font
            ws.cell(row=row, column=4, value=float(item.opening_balance)).font = normal_font
            row += 1


# ===== دوال مساعدة جديدة لـ PDF العربي =====

def _create_pdf_header(report_data, styles):
    """إنشاء رأس الصفحة"""
    header_data = [
        [process_arabic_text('نظام إدارة مخازن المحروقات'), '', process_arabic_text(f'تاريخ الطباعة: {create_arabic_date_string(datetime.now())}')]
    ]

    header_table = Table(header_data, colWidths=[200, 100, 200])
    header_table.setStyle(TableStyle([
        ('FONTNAME', (0, 0), (-1, -1), 'Arabic'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('ALIGN', (0, 0), (0, 0), 'RIGHT'),
        ('ALIGN', (2, 0), (2, 0), 'LEFT'),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ('LINEBELOW', (0, 0), (-1, -1), 1, colors.grey),
    ]))

    return header_table


def _create_report_info_section(report_data, styles):
    """إنشاء قسم معلومات التقرير"""
    content = []

    # معلومات التواريخ
    if report_data.get('from_date') and report_data.get('to_date'):
        from_date_ar = create_arabic_date_string(report_data['from_date']) if hasattr(report_data['from_date'], 'day') else str(report_data['from_date'])
        to_date_ar = create_arabic_date_string(report_data['to_date']) if hasattr(report_data['to_date'], 'day') else str(report_data['to_date'])

        date_info = process_arabic_text(f"الفترة: من {from_date_ar} إلى {to_date_ar}")
        content.append(Paragraph(date_info, styles['arabic_normal']))
        content.append(Spacer(1, 8))

    # معلومات المخزن
    if report_data.get('storage'):
        storage_info = process_arabic_text(f"المخزن: {report_data['storage'].name}")
        content.append(Paragraph(storage_info, styles['arabic_normal']))
        content.append(Spacer(1, 8))

    # معلومات المورد
    if report_data.get('supplier'):
        supplier_info = process_arabic_text(f"المورد: {report_data['supplier'].full_name}")
        content.append(Paragraph(supplier_info, styles['arabic_normal']))
        content.append(Spacer(1, 8))

    # معلومات المستفيد
    if report_data.get('beneficiary'):
        beneficiary_info = process_arabic_text(f"المستفيد: {report_data['beneficiary'].full_name}")
        content.append(Paragraph(beneficiary_info, styles['arabic_normal']))
        content.append(Spacer(1, 8))

    return content


def _create_pdf_footer(styles):
    """إنشاء تذييل الصفحة"""
    content = []

    footer_text = process_arabic_text('تم إنشاء هذا التقرير بواسطة نظام إدارة مخازن المحروقات')
    footer = Paragraph(footer_text, styles['arabic_center'])
    content.append(footer)

    return content
