{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - نظام إدارة مخازن المحروقات{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-3 mb-4">
        <div>
            <h1 class="h2 mb-2" style="background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 700;">
                <i class="fas fa-edit me-2"></i>{{ title }}
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'fuel_storage:dashboard' %}">لوحة التحكم</a></li>
                    {% block breadcrumb %}{% endblock %}
                    <li class="breadcrumb-item active">{{ title }}</li>
                </ol>
            </nav>
        </div>
        <div class="btn-toolbar">
            {% block header_buttons %}
            <a href="{% block back_url %}#{% endblock %}" class="btn btn-outline-secondary me-2">
                <i class="fas fa-arrow-right me-2"></i>العودة
            </a>
            {% endblock %}
        </div>
    </div>

    <!-- Form Section -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-form me-2"></i>{{ title }}
                    </h5>
                </div>
                <div class="card-body">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                <i class="fas fa-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}exclamation-circle{% elif message.tags == 'warning' %}exclamation-triangle{% else %}info-circle{% endif %} me-2"></i>
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}
                        
                        {% block form_content %}
                        <div class="row">
                            {% for field in form %}
                                <div class="col-md-{% block field_width %}6{% endblock %} mb-3">
                                    <label for="{{ field.id_for_label }}" class="form-label">
                                        {{ field.label }}
                                        {% if field.field.required %}
                                            <span class="text-danger">*</span>
                                        {% endif %}
                                    </label>
                                    {{ field }}
                                    {% if field.help_text %}
                                        <div class="form-text">{{ field.help_text }}</div>
                                    {% endif %}
                                    {% if field.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in field.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            {% endfor %}
                        </div>
                        {% endblock %}

                        <div class="d-flex justify-content-between align-items-center mt-4">
                            <div>
                                <span class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    الحقول المميزة بـ <span class="text-danger">*</span> مطلوبة
                                </span>
                            </div>
                            <div>
                                <a href="{% block cancel_url %}{% block back_url %}#{% endblock %}{% endblock %}" class="btn btn-secondary me-2">
                                    <i class="fas fa-times me-2"></i>إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>{{ submit_text|default:"حفظ" }}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom CSS for Forms -->
<style>
.form-control:focus,
.form-select:focus {
    border-color: #2563eb;
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

.form-label {
    font-weight: 600;
    color: #374151;
}

.invalid-feedback {
    font-size: 0.875rem;
}

.card {
    border: none;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.card-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 1px solid #e2e8f0;
}

.breadcrumb {
    background: none;
    padding: 0;
    margin: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "‹";
    font-weight: bold;
}

.breadcrumb-item a {
    color: #6b7280;
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: #2563eb;
}

.breadcrumb-item.active {
    color: #374151;
}
</style>

<!-- Form Validation JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Bootstrap form validation
    const forms = document.querySelectorAll('.needs-validation');
    
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
                
                // Show toast notification for validation errors
                if (typeof showToast !== 'undefined') {
                    showToast('خطأ في النموذج', 'يرجى تصحيح الأخطاء المحددة', 'error');
                }
            }
            
            form.classList.add('was-validated');
        });
    });
    
    // Auto-focus first input
    const firstInput = document.querySelector('.form-control, .form-select');
    if (firstInput) {
        firstInput.focus();
    }
});
</script>

{% endblock %}
