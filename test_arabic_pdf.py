#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
اختبار إنشاء PDF باللغة العربية
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fuel_storage_system.settings')
django.setup()

from fuel_storage.arabic_pdf_utils import (
    process_arabic_text, 
    create_arabic_paragraph_styles,
    format_arabic_number,
    create_arabic_date_string
)
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.lib.pagesizes import A4
from datetime import datetime
import io


def test_arabic_pdf():
    """اختبار إنشاء PDF باللغة العربية"""
    
    print("بدء اختبار PDF العربي...")
    
    # إنشاء buffer للـ PDF
    buffer = io.BytesIO()
    
    # إنشاء المستند
    doc = SimpleDocTemplate(
        buffer, 
        pagesize=A4, 
        rightMargin=50, 
        leftMargin=50, 
        topMargin=50, 
        bottomMargin=50
    )
    
    # إنشاء الأنماط
    styles = create_arabic_paragraph_styles()
    
    # إنشاء المحتوى
    story = []
    
    # العنوان
    title_text = process_arabic_text("تقرير اختبار النصوص العربية")
    title = Paragraph(title_text, styles['arabic_title'])
    story.append(title)
    story.append(Spacer(1, 20))
    
    # النص العادي
    normal_text = process_arabic_text("هذا نص تجريبي باللغة العربية لاختبار عرض النصوص في ملفات PDF")
    normal_para = Paragraph(normal_text, styles['arabic_normal'])
    story.append(normal_para)
    story.append(Spacer(1, 15))
    
    # العنوان الفرعي
    heading_text = process_arabic_text("الأرقام والتواريخ")
    heading = Paragraph(heading_text, styles['arabic_heading'])
    story.append(heading)
    story.append(Spacer(1, 10))
    
    # الأرقام
    number_text = process_arabic_text(f"رقم تجريبي: {format_arabic_number(12345.67)}")
    number_para = Paragraph(number_text, styles['arabic_normal'])
    story.append(number_para)
    story.append(Spacer(1, 10))
    
    # التاريخ
    date_text = process_arabic_text(f"التاريخ: {create_arabic_date_string(datetime.now())}")
    date_para = Paragraph(date_text, styles['arabic_normal'])
    story.append(date_para)
    story.append(Spacer(1, 15))
    
    # نص مختلط
    mixed_text = process_arabic_text("نص مختلط: English Text مع النص العربي والأرقام 123")
    mixed_para = Paragraph(mixed_text, styles['arabic_normal'])
    story.append(mixed_para)
    
    # إنشاء PDF
    try:
        doc.build(story)
        buffer.seek(0)
        
        # حفظ الملف
        with open('test_arabic_output.pdf', 'wb') as f:
            f.write(buffer.getvalue())
        
        print("✅ تم إنشاء ملف PDF بنجاح: test_arabic_output.pdf")
        print("يمكنك فتح الملف للتحقق من عرض النصوص العربية")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء PDF: {e}")
        return False
    
    return True


if __name__ == "__main__":
    success = test_arabic_pdf()
    if success:
        print("\n🎉 اختبار PDF العربي مكتمل بنجاح!")
    else:
        print("\n❌ فشل اختبار PDF العربي")
        sys.exit(1)
