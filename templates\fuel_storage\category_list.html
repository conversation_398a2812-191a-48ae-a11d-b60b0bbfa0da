{% extends 'base.html' %}

{% block title %}الأصناف - نظام إدارة مخازن المحروقات{% endblock %}

{% block content %}
<!-- Header Section -->
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-3 mb-4">
    <div>
        <h1 class="h2 mb-2" style="background: linear-gradient(135deg, #059669 0%, #047857 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 700;">
            <i class="fas fa-tags me-2"></i>إدارة الأصناف
        </h1>
        <p class="text-muted mb-0">عرض وإدارة جميع أصناف المحروقات في النظام</p>
    </div>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2" role="group">
            <button type="button" class="btn btn-outline-secondary active" onclick="toggleView('grid')" id="gridViewBtn">
                <i class="fas fa-th me-1"></i>شبكة
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="toggleView('list')" id="listViewBtn">
                <i class="fas fa-list me-1"></i>قائمة
            </button>
        </div>
        <a href="/admin/fuel_storage/category/add/" class="btn btn-success">
            <i class="fas fa-plus me-2"></i>إضافة صنف جديد
        </a>
    </div>
</div>

<!-- Search Section -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="input-group">
            <span class="input-group-text">
                <i class="fas fa-search"></i>
            </span>
            <input type="text" class="form-control" placeholder="البحث في الأصناف..." id="searchInput">
        </div>
    </div>
    <div class="col-md-4">
        <button type="button" class="btn btn-outline-success w-100" onclick="exportCategories()">
            <i class="fas fa-download me-2"></i>تصدير البيانات
        </button>
    </div>
</div>

{% if categories %}
    <!-- Grid View -->
    <div id="gridView" class="row">
        {% for category in categories %}
        <div class="col-md-6 col-lg-4 col-xl-3 mb-4 category-item" data-name="{{ category.name|lower }}">
            <div class="card stat-card h-100">
                <div class="card-body text-center position-relative">
                    <div class="category-icon mb-3">
                        <div class="icon-wrapper success">
                            <i class="fas fa-tag fa-lg"></i>
                        </div>
                    </div>

                    <h5 class="card-title mb-3">{{ category.name }}</h5>

                    <!-- Category Stats -->
                    <div class="row text-center mb-3">
                        <div class="col-6">
                            <div class="stat-mini">
                                <div class="stat-mini-number">{{ category.storage_items.count|default:0 }}</div>
                                <div class="stat-mini-label">في المخازن</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-mini">
                                <div class="stat-mini-number">{{ category.incoming_operations.count|default:0 }}</div>
                                <div class="stat-mini-label">عمليات وارد</div>
                            </div>
                        </div>
                    </div>

                    <div class="category-info mb-3">
                        <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>
                            أُضيف في {{ category.created_at|date:"Y-m-d" }}
                        </small>
                    </div>

                    <div class="d-flex gap-2">
                        <a href="/admin/fuel_storage/category/{{ category.id }}/change/" class="btn btn-sm btn-outline-success flex-fill">
                            <i class="fas fa-edit me-1"></i>تعديل
                        </a>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#"><i class="fas fa-eye me-2"></i>عرض التفاصيل</a></li>
                                <li><a class="dropdown-item" href="#"><i class="fas fa-chart-bar me-2"></i>الإحصائيات</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#"><i class="fas fa-trash me-2"></i>حذف</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- List View -->
    <div id="listView" class="d-none">
        <div class="card">
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>اسم الصنف</th>
                                <th>عدد المخازن</th>
                                <th>عمليات الوارد</th>
                                <th>عمليات الصادر</th>
                                <th>تاريخ الإضافة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for category in categories %}
                            <tr class="category-item" data-name="{{ category.name|lower }}">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="icon-wrapper success me-2" style="width: 2rem; height: 2rem;">
                                            <i class="fas fa-tag"></i>
                                        </div>
                                        <strong>{{ category.name }}</strong>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ category.storage_items.count|default:0 }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-success">{{ category.incoming_operations.count|default:0 }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-danger">{{ category.outgoing_operations.count|default:0 }}</span>
                                </td>
                                <td>{{ category.created_at|date:"Y-m-d" }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="/admin/fuel_storage/category/{{ category.id }}/change/" class="btn btn-outline-success">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-info">
                                            <i class="fas fa-chart-bar"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
{% else %}
    <div class="text-center py-5">
        <i class="fas fa-tags fa-4x text-muted mb-3"></i>
        <h4 class="text-muted">لا توجد أصناف مسجلة</h4>
        <p class="text-muted mb-4">ابدأ بإضافة صنف جديد لتصنيف المحروقات</p>
        <a href="/admin/fuel_storage/category/add/" class="btn btn-success">
            <i class="fas fa-plus me-2"></i>إضافة صنف جديد
        </a>
    </div>
{% endif %}

<!-- Custom CSS and JavaScript -->
<style>
.category-item {
    transition: all 0.3s ease;
}

.category-item.filtered-out {
    display: none !important;
}

#gridViewBtn.active,
#listViewBtn.active {
    background-color: var(--success-color);
    color: white;
    border-color: var(--success-color);
}

.stat-mini {
    padding: 0.5rem;
    border-radius: 0.5rem;
    background: rgba(5, 150, 105, 0.1);
}

.stat-mini-number {
    font-size: 1.25rem;
    font-weight: 700;
    color: #059669;
}

.stat-mini-label {
    font-size: 0.75rem;
    color: #64748b;
    font-weight: 500;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    const searchInput = document.getElementById('searchInput');

    function filterCategories() {
        const searchTerm = searchInput.value.toLowerCase();
        const categoryItems = document.querySelectorAll('.category-item');

        categoryItems.forEach(item => {
            const name = item.dataset.name;

            if (name.includes(searchTerm)) {
                item.classList.remove('filtered-out');
            } else {
                item.classList.add('filtered-out');
            }
        });
    }

    searchInput.addEventListener('input', filterCategories);
});

// Toggle between grid and list view
function toggleView(viewType) {
    const gridView = document.getElementById('gridView');
    const listView = document.getElementById('listView');
    const gridBtn = document.getElementById('gridViewBtn');
    const listBtn = document.getElementById('listViewBtn');

    if (viewType === 'grid') {
        gridView.classList.remove('d-none');
        listView.classList.add('d-none');
        gridBtn.classList.add('active');
        listBtn.classList.remove('active');
    } else {
        gridView.classList.add('d-none');
        listView.classList.remove('d-none');
        gridBtn.classList.remove('active');
        listBtn.classList.add('active');
    }
}

// Export functionality
function exportCategories() {
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التصدير...';
    button.disabled = true;

    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;

        const alert = document.createElement('div');
        alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
        alert.style.top = '20px';
        alert.style.right = '20px';
        alert.style.zIndex = '9999';
        alert.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>تم تصدير بيانات الأصناف بنجاح
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(alert);

        setTimeout(() => {
            alert.remove();
        }, 3000);
    }, 2000);
}
</script>

{% endblock %}
