#!/usr/bin/env python3
"""
سكريبت للتحقق من تثبيت جميع المكتبات المطلوبة
"""

import sys
import importlib

def check_module(module_name, display_name=None):
    """التحقق من تثبيت مكتبة معينة"""
    if display_name is None:
        display_name = module_name
    
    try:
        importlib.import_module(module_name)
        print(f"✅ {display_name} - مثبتة")
        return True
    except ImportError:
        print(f"❌ {display_name} - غير مثبتة")
        return False

def main():
    """التحقق من جميع المكتبات المطلوبة"""
    print("🔍 التحقق من تثبيت المكتبات المطلوبة...\n")
    
    # قائمة المكتبات المطلوبة
    required_modules = [
        ('django', 'Django'),
        ('PIL', 'Pillow'),
        ('reportlab', 'ReportLab'),
        ('openpyxl', 'OpenPyXL'),
        ('decouple', 'Python-decouple'),
    ]
    
    missing_modules = []
    
    for module_name, display_name in required_modules:
        if not check_module(module_name, display_name):
            missing_modules.append((module_name, display_name))
    
    print(f"\n📊 النتائج:")
    print(f"إجمالي المكتبات: {len(required_modules)}")
    print(f"مثبتة: {len(required_modules) - len(missing_modules)}")
    print(f"غير مثبتة: {len(missing_modules)}")
    
    if missing_modules:
        print(f"\n⚠️ المكتبات المفقودة:")
        for module_name, display_name in missing_modules:
            print(f"   - {display_name}")
        
        print(f"\n💡 لتثبيت المكتبات المفقودة:")
        print("على Windows: تشغيل install_dependencies.bat")
        print("على Linux/macOS: تشغيل ./install_dependencies.sh")
        print("أو يدوياً: pip install -r requirements.txt")
        
        return False
    else:
        print(f"\n🎉 جميع المكتبات مثبتة بنجاح!")
        print("يمكنك الآن تشغيل النظام باستخدام: python manage.py runserver")
        return True

if __name__ == '__main__':
    if main():
        sys.exit(0)
    else:
        sys.exit(1)
