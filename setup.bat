@echo off
echo إعداد نظام إدارة مخازن المحروقات...

REM إنشاء البيئة الافتراضية
echo إنشاء البيئة الافتراضية...
python -m venv venv

REM تفعيل البيئة الافتراضية
echo تفعيل البيئة الافتراضية...
call venv\Scripts\activate

REM تثبيت المكتبات المطلوبة
echo تثبيت المكتبات المطلوبة...
pip install -r requirements.txt

REM إنشاء المجلدات المطلوبة
echo إنشاء المجلدات المطلوبة...
mkdir media\incoming_operations 2>nul
mkdir media\outgoing_operations 2>nul
mkdir media\incoming_returns 2>nul
mkdir media\outgoing_returns 2>nul
mkdir media\damage_operations 2>nul
mkdir media\storage_transfers 2>nul
mkdir static 2>nul
mkdir logs 2>nul

REM إنشاء الهجرات
echo إنشاء الهجرات...
python manage.py makemigrations fuel_storage
python manage.py migrate

REM إنشاء مستخدم إداري
echo إنشاء مستخدم إداري...
echo من فضلك أدخل بيانات المستخدم الإداري:
python manage.py createsuperuser

REM تشغيل الخادم
echo تشغيل الخادم...
python manage.py runserver

pause
