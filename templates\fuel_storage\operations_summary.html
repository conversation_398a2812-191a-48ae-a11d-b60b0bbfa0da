{% extends 'base.html' %}

{% block title %}ملخص العمليات - نظام إدارة مخازن المحروقات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">ملخص العمليات الخاصة</h1>
</div>

<!-- إحصائيات العمليات -->
<div class="row mb-4">
    <div class="col-md-6 mb-4">
        <div class="card border-left-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            إجمالي عمليات التلف
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_damage_operations }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            إجمالي عمليات التحويل
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_storage_transfers }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exchange-alt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- أحدث العمليات -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-danger">أحدث عمليات التلف</h6>
            </div>
            <div class="card-body">
                {% if recent_damage_operations %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الرقم الورقي</th>
                                    <th>المخزن</th>
                                    <th>تاريخ التلف</th>
                                    <th>أنشئ بواسطة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for operation in recent_damage_operations %}
                                <tr>
                                    <td>{{ operation.paper_number }}</td>
                                    <td>{{ operation.storage.name }}</td>
                                    <td>{{ operation.damage_date|date:"Y-m-d" }}</td>
                                    <td>{{ operation.created_by.full_name|default:operation.created_by.username }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">لا توجد عمليات تلف حتى الآن</p>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">أحدث عمليات التحويل</h6>
            </div>
            <div class="card-body">
                {% if recent_storage_transfers %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الرقم الورقي</th>
                                    <th>من</th>
                                    <th>إلى</th>
                                    <th>تاريخ النقل</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transfer in recent_storage_transfers %}
                                <tr>
                                    <td>{{ transfer.paper_number }}</td>
                                    <td>{{ transfer.from_storage.name }}</td>
                                    <td>{{ transfer.to_storage.name }}</td>
                                    <td>{{ transfer.transfer_date|date:"Y-m-d" }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">لا توجد عمليات تحويل حتى الآن</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- روابط سريعة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">إجراءات سريعة</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="/admin/fuel_storage/damageoperation/add/" class="btn btn-outline-danger btn-block">
                            <i class="fas fa-plus me-1"></i>
                            إضافة عملية تلف
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="/admin/fuel_storage/storagetransfer/add/" class="btn btn-outline-primary btn-block">
                            <i class="fas fa-plus me-1"></i>
                            إضافة تحويل مخزني
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'fuel_storage:damage_operations_list' %}" class="btn btn-outline-secondary btn-block">
                            <i class="fas fa-list me-1"></i>
                            عرض عمليات التلف
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'fuel_storage:storage_transfers_list' %}" class="btn btn-outline-secondary btn-block">
                            <i class="fas fa-list me-1"></i>
                            عرض التحويلات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- معلومات إضافية -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card border-left-danger">
            <div class="card-header bg-danger text-white">
                <h6 class="m-0">عمليات التلف</h6>
            </div>
            <div class="card-body">
                <p class="card-text">تستخدم لتسجيل الكميات التالفة وخصمها من المخزون نهائياً.</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-minus-circle text-danger me-2"></i>خصم نهائي من المخزون</li>
                    <li><i class="fas fa-file-alt text-info me-2"></i>تسجيل أسباب التلف</li>
                    <li><i class="fas fa-paperclip text-secondary me-2"></i>إرفاق ملفات داعمة</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card border-left-primary">
            <div class="card-header bg-primary text-white">
                <h6 class="m-0">التحويل المخزني</h6>
            </div>
            <div class="card-body">
                <p class="card-text">تستخدم لنقل الكميات بين المخازن المختلفة.</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-exchange-alt text-primary me-2"></i>نقل بين المخازن</li>
                    <li><i class="fas fa-balance-scale text-success me-2"></i>الحفاظ على الكمية الإجمالية</li>
                    <li><i class="fas fa-shield-alt text-warning me-2"></i>التحقق من توفر الكميات</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
