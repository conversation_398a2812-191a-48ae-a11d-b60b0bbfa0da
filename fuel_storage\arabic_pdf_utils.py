# -*- coding: utf-8 -*-
"""
مساعدات لإنشاء ملفات PDF باللغة العربية
"""

import os
import platform
from django.conf import settings
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.styles import ParagraphStyle
from reportlab.lib.enums import TA_RIGHT, TA_CENTER, TA_LEFT
from bidi.algorithm import get_display
import arabic_reshaper
from .font_config import get_best_arabic_font, get_font_config


def get_system_arabic_font():
    """البحث عن خط عربي في النظام"""
    system = platform.system()
    
    if system == "Windows":
        # مسارات الخطوط في Windows
        font_paths = [
            "C:/Windows/Fonts/arial.ttf",
            "C:/Windows/Fonts/tahoma.ttf",
            "C:/Windows/Fonts/calibri.ttf",
            "C:/Windows/Fonts/times.ttf",
        ]
    elif system == "Darwin":  # macOS
        font_paths = [
            "/System/Library/Fonts/Arial.ttf",
            "/System/Library/Fonts/Times.ttc",
            "/Library/Fonts/Arial.ttf",
        ]
    else:  # Linux
        font_paths = [
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
            "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",
            "/usr/share/fonts/TTF/arial.ttf",
        ]
    
    # البحث عن أول خط متاح
    for font_path in font_paths:
        if os.path.exists(font_path):
            return font_path
    
    return None


def register_arabic_fonts():
    """تسجيل الخطوط العربية"""
    try:
        # الحصول على أفضل خط متاح
        font_info = get_best_arabic_font()

        if font_info:
            font_name, font_path = font_info

            # تسجيل الخط العادي
            pdfmetrics.registerFont(TTFont('Arabic', font_path))

            # محاولة تسجيل الخط العريض (نفس الخط مع تأثير عريض)
            try:
                pdfmetrics.registerFont(TTFont('Arabic-Bold', font_path))
            except:
                # في حالة فشل تسجيل الخط العريض، استخدم نفس الخط
                pdfmetrics.registerFont(TTFont('Arabic-Bold', font_path))

            print(f"تم تسجيل الخط بنجاح: {font_name}")
            return True

    except Exception as e:
        print(f"خطأ في تسجيل الخطوط العربية: {e}")

    # في حالة فشل تسجيل أي خط، استخدم الخط الافتراضي
    print("تحذير: لم يتم العثور على خط عربي مناسب، سيتم استخدام الخط الافتراضي")
    return False


def process_arabic_text(text):
    """معالجة النص العربي للعرض الصحيح في PDF"""
    if not text:
        return ""
    
    try:
        # إعادة تشكيل النص العربي
        reshaped_text = arabic_reshaper.reshape(str(text))
        # تطبيق خوارزمية BiDi للعرض الصحيح
        bidi_text = get_display(reshaped_text)
        return bidi_text
    except Exception as e:
        print(f"خطأ في معالجة النص العربي: {e}")
        return str(text)


def create_arabic_paragraph_styles():
    """إنشاء أنماط الفقرات العربية"""

    # تسجيل الخطوط
    fonts_registered = register_arabic_fonts()
    font_name = 'Arabic' if fonts_registered else 'Helvetica'
    font_name_bold = 'Arabic-Bold' if fonts_registered else 'Helvetica-Bold'

    # الحصول على إعدادات الخطوط
    font_config = get_font_config()
    
    styles = {
        'arabic_normal': ParagraphStyle(
            'ArabicNormal',
            fontName=font_name,
            fontSize=font_config['default_size'],
            alignment=TA_RIGHT,
            rightIndent=0,
            leftIndent=0,
            spaceAfter=6,
            leading=font_config['default_size'] * font_config['line_height_factor']
        ),

        'arabic_title': ParagraphStyle(
            'ArabicTitle',
            fontName=font_name_bold,
            fontSize=font_config['title_size'],
            alignment=TA_CENTER,
            spaceAfter=20,
            spaceBefore=10,
            leading=font_config['title_size'] * font_config['line_height_factor']
        ),

        'arabic_heading': ParagraphStyle(
            'ArabicHeading',
            fontName=font_name_bold,
            fontSize=font_config['heading_size'],
            alignment=TA_RIGHT,
            spaceAfter=12,
            spaceBefore=12,
            leading=font_config['heading_size'] * font_config['line_height_factor']
        ),

        'arabic_small': ParagraphStyle(
            'ArabicSmall',
            fontName=font_name,
            fontSize=font_config['small_size'],
            alignment=TA_RIGHT,
            spaceAfter=4,
            leading=font_config['small_size'] * font_config['line_height_factor']
        ),

        'arabic_center': ParagraphStyle(
            'ArabicCenter',
            fontName=font_name,
            fontSize=font_config['default_size'],
            alignment=TA_CENTER,
            spaceAfter=6,
            leading=font_config['default_size'] * font_config['line_height_factor']
        ),

        'arabic_left': ParagraphStyle(
            'ArabicLeft',
            fontName=font_name,
            fontSize=font_config['default_size'],
            alignment=TA_LEFT,
            spaceAfter=6,
            leading=font_config['default_size'] * font_config['line_height_factor']
        )
    }
    
    return styles


def create_arabic_table_data(data, headers=None):
    """إنشاء بيانات جدول مع معالجة النصوص العربية"""
    processed_data = []
    
    # معالجة العناوين
    if headers:
        processed_headers = [process_arabic_text(header) for header in headers]
        processed_data.append(processed_headers)
    
    # معالجة البيانات
    for row in data:
        processed_row = []
        for cell in row:
            if isinstance(cell, (int, float)):
                # تنسيق الأرقام
                processed_row.append(f"{cell:,.2f}".replace(',', '٬'))
            else:
                processed_row.append(process_arabic_text(str(cell)))
        processed_data.append(processed_row)
    
    return processed_data


def format_arabic_number(number):
    """تنسيق الأرقام للعرض العربي"""
    if isinstance(number, (int, float)):
        # تحويل الأرقام الإنجليزية إلى عربية
        english_digits = '0123456789'
        arabic_digits = '٠١٢٣٤٥٦٧٨٩'
        
        formatted = f"{number:,.2f}"
        for eng, ara in zip(english_digits, arabic_digits):
            formatted = formatted.replace(eng, ara)
        
        # استبدال الفاصلة الإنجليزية بالعربية
        formatted = formatted.replace(',', '٬')
        formatted = formatted.replace('.', '٫')
        
        return formatted
    
    return str(number)


def create_arabic_date_string(date_obj):
    """إنشاء نص تاريخ عربي"""
    if not date_obj:
        return ""
    
    try:
        # أسماء الأشهر العربية
        arabic_months = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ]
        
        day = format_arabic_number(date_obj.day)
        month = arabic_months[date_obj.month - 1]
        year = format_arabic_number(date_obj.year)
        
        return f"{day} {month} {year}"
        
    except Exception as e:
        print(f"خطأ في تنسيق التاريخ: {e}")
        return str(date_obj)
