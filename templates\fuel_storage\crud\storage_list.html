{% extends 'base.html' %}
{% load static %}

{% block title %}إدارة المخازن - نظام إدارة مخازن المحروقات{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-3 mb-4">
        <div>
            <h1 class="h2 mb-2" style="background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 700;">
                <i class="fas fa-warehouse me-2"></i>إدارة المخازن
            </h1>
            <p class="text-muted mb-0">عرض وإدارة جميع المخازن في النظام ({{ total_count }} مخزن)</p>
        </div>
        <div class="btn-toolbar">
            <a href="{% url 'fuel_storage:storage_create_crud' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إضافة مخزن جديد
            </a>
        </div>
    </div>

    <!-- Search and Filter Section -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">البحث</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" name="search" class="form-control" 
                               placeholder="البحث في المخازن..." 
                               value="{{ search_query }}">
                    </div>
                </div>
                <div class="col-md-3">
                    <label class="form-label">التصنيف</label>
                    {{ filter_form.classification }}
                </div>
                <div class="col-md-3">
                    <label class="form-label">الحالة</label>
                    {{ filter_form.is_active }}
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-outline-primary w-100">
                        <i class="fas fa-filter me-2"></i>تصفية
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Messages -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                <i class="fas fa-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}exclamation-circle{% elif message.tags == 'warning' %}exclamation-triangle{% else %}info-circle{% endif %} me-2"></i>
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endfor %}
    {% endif %}

    <!-- Storage List -->
    {% if storages %}
        <div class="row">
            {% for storage in storages %}
                <div class="col-lg-6 col-xl-4 mb-4">
                    <div class="card storage-card h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-warehouse text-primary me-2"></i>
                                    {{ storage.name }}
                                </h5>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                            type="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a class="dropdown-item" href="{% url 'fuel_storage:storage_update_crud' storage.pk %}">
                                                <i class="fas fa-edit me-2"></i>تعديل
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="{% url 'fuel_storage:storage_report' %}?storage_id={{ storage.id }}">
                                                <i class="fas fa-chart-bar me-2"></i>التقرير
                                            </a>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <a class="dropdown-item text-danger" 
                                               href="{% url 'fuel_storage:storage_delete_crud' storage.pk %}"
                                               onclick="return confirm('هل أنت متأكد من حذف هذا المخزن؟')">
                                                <i class="fas fa-trash me-2"></i>حذف
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>

                            <div class="storage-info">
                                <div class="info-item mb-2">
                                    <span class="badge bg-{% if storage.classification == 'main' %}primary{% elif storage.classification == 'secondary' %}success{% else %}warning{% endif %}">
                                        {{ storage.get_classification_display }}
                                    </span>
                                    {% if storage.is_active %}
                                        <span class="badge bg-success ms-2">نشط</span>
                                    {% else %}
                                        <span class="badge bg-secondary ms-2">غير نشط</span>
                                    {% endif %}
                                </div>
                                
                                <div class="info-item mb-2">
                                    <i class="fas fa-user text-muted me-2"></i>
                                    <span class="text-muted">أمين المخزن:</span>
                                    <strong>{{ storage.keeper_name }}</strong>
                                </div>
                                
                                <div class="info-item mb-2">
                                    <i class="fas fa-phone text-muted me-2"></i>
                                    <span class="text-muted">الهاتف:</span>
                                    <strong>{{ storage.phone_number }}</strong>
                                </div>
                                
                                <div class="info-item mb-3">
                                    <i class="fas fa-calendar text-muted me-2"></i>
                                    <span class="text-muted">تاريخ الإنشاء:</span>
                                    <strong>{{ storage.created_at|date:"Y-m-d" }}</strong>
                                </div>
                            </div>

                            <!-- Storage Stats -->
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="stat-mini">
                                        <div class="stat-mini-number">{{ storage.storage_items.count|default:0 }}</div>
                                        <div class="stat-mini-label">أصناف</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-mini">
                                        <div class="stat-mini-number">{{ storage.incoming_operations.count|default:0 }}</div>
                                        <div class="stat-mini-label">وارد</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="stat-mini">
                                        <div class="stat-mini-number">{{ storage.outgoing_operations.count|default:0 }}</div>
                                        <div class="stat-mini-label">صادر</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card-footer bg-transparent">
                            <div class="d-flex gap-2">
                                <a href="{% url 'fuel_storage:storage_update_crud' storage.pk %}" 
                                   class="btn btn-sm btn-outline-primary flex-fill">
                                    <i class="fas fa-edit me-1"></i>تعديل
                                </a>
                                <a href="{% url 'fuel_storage:storage_report' %}?storage_id={{ storage.id }}" 
                                   class="btn btn-sm btn-outline-info flex-fill">
                                    <i class="fas fa-chart-bar me-1"></i>التقرير
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if storages.has_other_pages %}
            <nav aria-label="تصفح الصفحات">
                <ul class="pagination justify-content-center">
                    {% if storages.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ storages.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    {% endif %}
                    
                    {% for num in storages.paginator.page_range %}
                        {% if storages.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% elif num > storages.number|add:'-3' and num < storages.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if storages.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ storages.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        {% endif %}
    {% else %}
        <div class="text-center py-5">
            <i class="fas fa-warehouse fa-4x text-muted mb-3"></i>
            <h4 class="text-muted">لا توجد مخازن</h4>
            <p class="text-muted mb-4">
                {% if search_query %}
                    لم يتم العثور على مخازن تطابق البحث "{{ search_query }}"
                {% else %}
                    لا توجد مخازن مسجلة في النظام
                {% endif %}
            </p>
            <a href="{% url 'fuel_storage:storage_create_crud' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إضافة مخزن جديد
            </a>
        </div>
    {% endif %}
</div>

<!-- Custom CSS -->
<style>
.storage-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.storage-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.info-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.stat-mini {
    padding: 0.75rem;
    border-radius: 0.5rem;
    background: rgba(37, 99, 235, 0.1);
    transition: all 0.3s ease;
}

.stat-mini:hover {
    background: rgba(37, 99, 235, 0.2);
}

.stat-mini-number {
    font-size: 1.25rem;
    font-weight: 700;
    color: #2563eb;
}

.stat-mini-label {
    font-size: 0.75rem;
    color: #64748b;
    font-weight: 500;
}
</style>

{% endblock %}
