-- إدراج بيانات تجريبية للعمليات الجديدة

-- إدراج بيانات تجريبية لعمليات التلف (يتطلب وجود مخازن وأصناف)
-- ملاحظة: هذه البيانات تجريبية ويجب تعديلها حسب البيانات الموجودة في النظام

-- إدراج أسباب تلف إضافية في جدول الخيارات (هذا مجرد مثال)
-- الأسباب محددة في النموذج كخيارات ثابتة

-- إدراج بيانات تجريبية لأسباب التحويل
-- الأسباب محددة في النموذج كخيارات ثابتة

-- ملاحظة مهمة: 
-- لا يمكن إدراج بيانات تجريبية للعمليات مباشرة لأنها تتطلب:
-- 1. وجود مخازن فعلية
-- 2. وجود أصناف فعلية
-- 3. وجود كميات متوفرة في المخازن
-- 4. مستخدمين مسجلين في النظام

-- يُنصح بإنشاء هذه العمليات من خلال واجهة الإدارة بعد:
-- 1. إنشاء المخازن والأصناف
-- 2. إجراء عمليات وارد لتوفير كميات في المخازن
-- 3. ثم إنشاء عمليات التلف والتحويل

-- مثال على البيانات المطلوبة (للمرجع فقط):
-- INSERT INTO fuel_storage_damageoperation (storage_id, damage_date, paper_number, deliverer_name, deliverer_job_number, receiver_name, receiver_job_number, statement, created_by_id, created_at)
-- VALUES (1, datetime('now'), 'DMG-001', 'أحمد محمد', 'EMP001', 'سالم علي', 'EMP002', 'تلف بسبب انتهاء الصلاحية', 1, datetime('now'));
