{% extends 'base.html' %}

{% block title %}المستفيدون - نظام إدارة مخازن المحروقات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">المستفيدون</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="/admin/fuel_storage/beneficiary/add/" class="btn btn-sm btn-outline-secondary">
            <i class="fas fa-plus me-1"></i>
            إضافة مستفيد جديد
        </a>
    </div>
</div>

{% if beneficiaries %}
    <div class="row">
        {% for beneficiary in beneficiaries %}
        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">{{ beneficiary.full_name }}</h5>
                    <p class="card-text">
                        <i class="fas fa-phone me-2"></i>{{ beneficiary.phone_number }}
                    </p>
                    <p class="card-text">
                        <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>
                            أُضيف في {{ beneficiary.created_at|date:"Y-m-d" }}
                        </small>
                    </p>
                    <a href="/admin/fuel_storage/beneficiary/{{ beneficiary.id }}/change/" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-edit me-1"></i>تعديل
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
{% else %}
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        لا يوجد مستفيدون مسجلون حتى الآن.
    </div>
{% endif %}
{% endblock %}
