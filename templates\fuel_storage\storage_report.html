{% extends 'base.html' %}

{% block title %}تقارير المخازن - نظام إدارة مخازن المحروقات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">تقارير المخازن</h1>
</div>

<!-- فلتر اختيار المخزن -->
<div class="row mb-4">
    <div class="col-md-6">
        <form method="get" class="d-flex">
            <select name="storage_id" class="form-select me-2" onchange="this.form.submit()">
                <option value="">اختر المخزن</option>
                {% for storage in storages %}
                    <option value="{{ storage.id }}" {% if selected_storage and selected_storage.id == storage.id %}selected{% endif %}>
                        {{ storage.name }}
                    </option>
                {% endfor %}
            </select>
        </form>
    </div>
</div>

{% if selected_storage %}
    <!-- معلومات المخزن -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">معلومات المخزن: {{ selected_storage.name }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <strong>التصنيف:</strong> {{ selected_storage.get_classification_display }}
                        </div>
                        <div class="col-md-3">
                            <strong>أمين المخزن:</strong> {{ selected_storage.keeper_name }}
                        </div>
                        <div class="col-md-3">
                            <strong>الهاتف:</strong> {{ selected_storage.phone_number }}
                        </div>
                        <div class="col-md-3">
                            <strong>تاريخ الإنشاء:</strong> {{ selected_storage.created_at|date:"Y-m-d" }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات العمليات -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card text-white bg-success">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ incoming_count }}</h4>
                            <p>عمليات الوارد</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-arrow-down fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card text-white bg-danger">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ outgoing_count }}</h4>
                            <p>عمليات الصادر</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-arrow-up fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- أصناف المخزن -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">أصناف المخزن والكميات الحالية</h5>
        </div>
        <div class="card-body">
            {% if storage_items %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>الصنف</th>
                                <th>وحدة القياس</th>
                                <th>الرصيد الافتتاحي</th>
                                <th>الكمية الحالية</th>
                                <th>آخر تحديث</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in storage_items %}
                            <tr>
                                <td>{{ item.category.name }}</td>
                                <td>{{ item.get_unit_of_measure_display }}</td>
                                <td>{{ item.opening_balance|floatformat:3 }}</td>
                                <td>{{ item.current_quantity|floatformat:3 }}</td>
                                <td>{{ item.updated_at|date:"Y-m-d H:i" }}</td>
                                <td>
                                    {% if item.current_quantity <= 0 %}
                                        <span class="badge bg-danger">نفدت الكمية</span>
                                    {% elif item.current_quantity <= 100 %}
                                        <span class="badge bg-warning">كمية منخفضة</span>
                                    {% else %}
                                        <span class="badge bg-success">متوفر</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    لا توجد أصناف في هذا المخزن حتى الآن.
                </div>
            {% endif %}
        </div>
    </div>
{% else %}
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        يرجى اختيار مخزن لعرض التقرير الخاص به.
    </div>
{% endif %}
{% endblock %}
