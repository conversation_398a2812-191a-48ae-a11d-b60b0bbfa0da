{% extends 'base.html' %}

{% block title %}مرتجعات الوارد - نظام إدارة مخازن المحروقات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">مرتجعات الوارد</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="/admin/fuel_storage/incomingreturn/add/" class="btn btn-sm btn-outline-secondary">
            <i class="fas fa-plus me-1"></i>
            إضافة مرتجع وارد جديد
        </a>
    </div>
</div>

{% if returns %}
    <div class="table-responsive">
        <table class="table table-striped table-sm">
            <thead>
                <tr>
                    <th>الرقم الورقي</th>
                    <th>عملية الوارد الأصلية</th>
                    <th>المخزن</th>
                    <th>المورد</th>
                    <th>المحطة</th>
                    <th>تاريخ المرتجع</th>
                    <th>المسلم</th>
                    <th>المستلم</th>
                    <th>أنشئ بواسطة</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for return in returns %}
                <tr>
                    <td>{{ return.paper_number }}</td>
                    <td>
                        <span class="badge bg-info">{{ return.incoming_operation.paper_number }}</span>
                    </td>
                    <td>{{ return.storage.name }}</td>
                    <td>{{ return.supplier.full_name }}</td>
                    <td>{{ return.station.name }}</td>
                    <td>{{ return.return_date|date:"Y-m-d H:i" }}</td>
                    <td>{{ return.deliverer_name }}</td>
                    <td>{{ return.receiver_name }}</td>
                    <td>{{ return.created_by.full_name|default:return.created_by.username }}</td>
                    <td>
                        <a href="/admin/fuel_storage/incomingreturn/{{ return.id }}/change/" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-edit"></i>
                        </a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
{% else %}
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        لا توجد مرتجعات وارد مسجلة حتى الآن.
    </div>
{% endif %}
{% endblock %}
