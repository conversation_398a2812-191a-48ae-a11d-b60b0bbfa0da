# دليل حل المشاكل - نظام إدارة مخازن المحروقات

## مشاكل شائعة وحلولها

### 1. خطأ ModuleNotFoundError: No module named 'reportlab'

**المشكلة:** عدم تثبيت مكتبة reportlab المطلوبة لإنشاء ملفات PDF.

**الحل:**
\`\`\`bash
# الحل السريع
pip install reportlab==4.0.7

# أو تثبيت جميع المكتبات
pip install -r requirements.txt
\`\`\`

### 2. خطأ في تثبيت Pillow على Windows

**المشكلة:** فشل تثبيت مكتبة Pillow على نظام Windows.

**الحل:**
\`\`\`bash
# تحديث pip أولاً
python -m pip install --upgrade pip

# ثم تثبيت Pillow
pip install Pillow==10.1.0
\`\`\`

### 3. خطأ Permission Denied أثناء التثبيت

**المشكلة:** عدم وجود صلاحيات كافية للتثبيت.

**الحل:**
\`\`\`bash
# على Linux/macOS
sudo pip install -r requirements.txt

# أو استخدام البيئة الافتراضية
python -m venv venv
source venv/bin/activate  # Linux/macOS
# venv\Scripts\activate   # Windows
pip install -r requirements.txt
\`\`\`

### 4. خطأ في قاعدة البيانات

**المشكلة:** خطأ "no such table" أو مشاكل في قاعدة البيانات.

**الحل:**
\`\`\`bash
# حذف قاعدة البيانات الحالية
rm db.sqlite3

# حذف ملفات الهجرة
rm fuel_storage/migrations/00*.py

# إنشاء هجرات جديدة
python manage.py makemigrations fuel_storage
python manage.py migrate

# إنشاء مستخدم إداري جديد
python manage.py createsuperuser
\`\`\`

### 5. مشكلة في تشغيل الخادم

**المشكلة:** خطأ "Port already in use" أو مشاكل في تشغيل الخادم.

**الحل:**
\`\`\`bash
# تشغيل على منفذ مختلف
python manage.py runserver 8001

# أو إيقاف العمليات المتعارضة
# على Linux/macOS
lsof -ti:8000 | xargs kill

# على Windows
netstat -ano | findstr :8000
taskkill /PID <PID_NUMBER> /F
\`\`\`

### 6. مشكلة في الملفات الثابتة (Static Files)

**المشكلة:** عدم ظهور CSS أو JavaScript بشكل صحيح.

**الحل:**
\`\`\`bash
# جمع الملفات الثابتة
python manage.py collectstatic

# التأكد من إعدادات STATIC_URL في settings.py
\`\`\`

### 7. مشكلة في رفع الملفات

**المشكلة:** خطأ في رفع الملفات أو عدم ظهور الصور.

**الحل:**
\`\`\`bash
# إنشاء مجلدات الوسائط
mkdir -p media/incoming_operations
mkdir -p media/outgoing_operations
mkdir -p media/incoming_returns
mkdir -p media/outgoing_returns
mkdir -p media/damage_operations
mkdir -p media/storage_transfers

# التأكد من الصلاحيات على Linux/macOS
chmod 755 media/
\`\`\`

## أوامر مفيدة للتشخيص

### التحقق من تثبيت المكتبات
\`\`\`bash
python check_dependencies.py
\`\`\`

### التحقق من إعدادات Django
\`\`\`bash
python manage.py check
\`\`\`

### عرض معلومات قاعدة البيانات
\`\`\`bash
python manage.py showmigrations
\`\`\`

### إنشاء بيانات تجريبية
\`\`\`bash
python scripts/load_initial_data.py
\`\`\`

## نصائح للأداء الأفضل

### 1. استخدام البيئة الافتراضية
\`\`\`bash
# إنشاء بيئة افتراضية جديدة
python -m venv fuel_storage_env

# تفعيلها
source fuel_storage_env/bin/activate  # Linux/macOS
fuel_storage_env\Scripts\activate     # Windows
\`\`\`

### 2. تحديث المكتبات بانتظام
\`\`\`bash
pip list --outdated
pip install --upgrade package_name
\`\`\`

### 3. النسخ الاحتياطي
\`\`\`bash
# نسخ احتياطي لقاعدة البيانات
cp db.sqlite3 backup_$(date +%Y%m%d).sqlite3

# نسخ احتياطي للملفات المرفوعة
tar -czf media_backup_$(date +%Y%m%d).tar.gz media/
\`\`\`

## طلب المساعدة

إذا واجهت مشكلة لم تُذكر هنا:

1. تحقق من ملفات السجلات في مجلد `logs/`
2. شغل `python check_dependencies.py` للتأكد من المكتبات
3. تأكد من وجود جميع المجلدات المطلوبة
4. راجع رسائل الخطأ في terminal بعناية

---

**ملاحظة:** تأكد دائماً من تفعيل البيئة الافتراضية قبل تشغيل أي أوامر Python.
