# Generated by Django 4.2.7 on 2025-08-03 23:38

from django.conf import settings
import django.contrib.auth.models
import django.contrib.auth.validators
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('full_name', models.CharField(max_length=100, verbose_name='الاسم الكامل')),
                ('user_type', models.CharField(choices=[('manager', 'مدير'), ('operator', 'مشغل')], default='operator', max_length=20, verbose_name='نوع المستخدم')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'مستخدم',
                'verbose_name_plural': 'المستخدمون',
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='Beneficiary',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('full_name', models.CharField(max_length=100, verbose_name='الاسم الكامل')),
                ('phone_number', models.CharField(max_length=17, validators=[django.core.validators.RegexValidator(message="رقم الهاتف يجب أن يكون بالصيغة: '+999999999'. حتى 15 رقم مسموح.", regex='^\\+?1?\\d{9,15}$')], verbose_name='رقم الهاتف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'مستفيد',
                'verbose_name_plural': 'المستفيدون',
            },
        ),
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='اسم الصنف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'صنف',
                'verbose_name_plural': 'الأصناف',
            },
        ),
        migrations.CreateModel(
            name='DamageOperation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('damage_date', models.DateTimeField(verbose_name='تاريخ التلف')),
                ('paper_number', models.CharField(max_length=50, verbose_name='الرقم الورقي')),
                ('deliverer_name', models.CharField(max_length=100, verbose_name='اسم المسلم')),
                ('deliverer_job_number', models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمسلم')),
                ('receiver_name', models.CharField(max_length=100, verbose_name='اسم المستلم')),
                ('receiver_job_number', models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمستلم')),
                ('statement', models.TextField(verbose_name='البيان')),
                ('is_locked', models.BooleanField(default=False, verbose_name='مقفل للتعديل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'عملية تلف',
                'verbose_name_plural': 'عمليات التلف',
            },
        ),
        migrations.CreateModel(
            name='IncomingOperation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('operation_date', models.DateTimeField(verbose_name='تاريخ عملية الوارد')),
                ('paper_number', models.CharField(max_length=50, verbose_name='الرقم الورقي')),
                ('supply_document_number', models.CharField(max_length=50, verbose_name='رقم سند التوريد')),
                ('deliverer_name', models.CharField(max_length=100, verbose_name='اسم المسلم')),
                ('deliverer_job_number', models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمسلم')),
                ('receiver_name', models.CharField(max_length=100, verbose_name='اسم المستلم')),
                ('receiver_job_number', models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمستلم')),
                ('statement', models.TextField(verbose_name='البيان')),
                ('is_locked', models.BooleanField(default=False, verbose_name='مقفل للتعديل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'عملية وارد',
                'verbose_name_plural': 'عمليات الوارد',
            },
        ),
        migrations.CreateModel(
            name='IncomingReturn',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('return_date', models.DateTimeField(verbose_name='تاريخ المرتجع')),
                ('paper_number', models.CharField(max_length=50, verbose_name='الرقم الورقي')),
                ('deliverer_name', models.CharField(max_length=100, verbose_name='اسم المسلم')),
                ('deliverer_job_number', models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمسلم')),
                ('receiver_name', models.CharField(max_length=100, verbose_name='اسم المستلم')),
                ('receiver_job_number', models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمستلم')),
                ('statement', models.TextField(verbose_name='البيان')),
                ('is_locked', models.BooleanField(default=False, verbose_name='مقفل للتعديل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('incoming_operation', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.incomingoperation', verbose_name='عملية الوارد المراد الإرجاع منها')),
            ],
            options={
                'verbose_name': 'مرتجع وارد',
                'verbose_name_plural': 'مرتجعات الوارد',
            },
        ),
        migrations.CreateModel(
            name='OutgoingOperation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('operation_date', models.DateTimeField(verbose_name='تاريخ عملية الصادر')),
                ('paper_number', models.CharField(max_length=50, verbose_name='الرقم الورقي')),
                ('deliverer_name', models.CharField(max_length=100, verbose_name='اسم المسلم')),
                ('deliverer_job_number', models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمسلم')),
                ('receiver_name', models.CharField(max_length=100, verbose_name='اسم المستلم')),
                ('receiver_job_number', models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمستلم')),
                ('statement', models.TextField(verbose_name='البيان')),
                ('is_locked', models.BooleanField(default=False, verbose_name='مقفل للتعديل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('beneficiary', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.beneficiary', verbose_name='المستفيد')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'عملية صادر',
                'verbose_name_plural': 'عمليات الصادر',
            },
        ),
        migrations.CreateModel(
            name='OutgoingReturn',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('return_date', models.DateTimeField(verbose_name='تاريخ المرتجع')),
                ('paper_number', models.CharField(max_length=50, verbose_name='الرقم الورقي')),
                ('deliverer_name', models.CharField(max_length=100, verbose_name='اسم المسلم')),
                ('deliverer_job_number', models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمسلم')),
                ('receiver_name', models.CharField(max_length=100, verbose_name='اسم المستلم')),
                ('receiver_job_number', models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمستلم')),
                ('statement', models.TextField(verbose_name='البيان')),
                ('is_locked', models.BooleanField(default=False, verbose_name='مقفل للتعديل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('outgoing_operation', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.outgoingoperation', verbose_name='عملية الصادر المراد الإرجاع منها')),
            ],
            options={
                'verbose_name': 'مرتجع صادر',
                'verbose_name_plural': 'مرتجعات الصادر',
            },
        ),
        migrations.CreateModel(
            name='Station',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم المحطة')),
                ('address', models.TextField(verbose_name='العنوان')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'محطة',
                'verbose_name_plural': 'المحطات',
            },
        ),
        migrations.CreateModel(
            name='Storage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='اسم المخزن')),
                ('classification', models.CharField(choices=[('main', 'مخزن رئيسي'), ('secondary', 'مخزن فرعي'), ('temporary', 'مخزن مؤقت')], max_length=20, verbose_name='التصنيف')),
                ('keeper_name', models.CharField(max_length=100, verbose_name='أمين المخزن')),
                ('phone_number', models.CharField(max_length=17, validators=[django.core.validators.RegexValidator(message="رقم الهاتف يجب أن يكون بالصيغة: '+999999999'. حتى 15 رقم مسموح.", regex='^\\+?1?\\d{9,15}$')], verbose_name='هاتف المخزن')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'مخزن',
                'verbose_name_plural': 'المخازن',
            },
        ),
        migrations.CreateModel(
            name='StorageTransfer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transfer_date', models.DateTimeField(verbose_name='تاريخ النقل')),
                ('paper_number', models.CharField(max_length=50, verbose_name='الرقم الورقي')),
                ('deliverer_name', models.CharField(max_length=100, verbose_name='اسم المسلم')),
                ('deliverer_job_number', models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمسلم')),
                ('receiver_name', models.CharField(max_length=100, verbose_name='اسم المستلم')),
                ('receiver_job_number', models.CharField(max_length=50, verbose_name='الرقم الوظيفي للمستلم')),
                ('statement', models.TextField(verbose_name='البيان')),
                ('is_locked', models.BooleanField(default=False, verbose_name='مقفل للتعديل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('from_storage', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='transfers_from', to='fuel_storage.storage', verbose_name='المخزن المحول منه')),
                ('to_storage', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='transfers_to', to='fuel_storage.storage', verbose_name='المخزن المحول إليه')),
            ],
            options={
                'verbose_name': 'عملية تحويل مخزني',
                'verbose_name_plural': 'عمليات التحويل المخزني',
            },
        ),
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('full_name', models.CharField(max_length=100, verbose_name='الاسم الكامل')),
                ('phone_number', models.CharField(max_length=17, validators=[django.core.validators.RegexValidator(message="رقم الهاتف يجب أن يكون بالصيغة: '+999999999'. حتى 15 رقم مسموح.", regex='^\\+?1?\\d{9,15}$')], verbose_name='رقم الهاتف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'مورد',
                'verbose_name_plural': 'الموردون',
            },
        ),
        migrations.CreateModel(
            name='StorageTransferItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transferred_quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية المحولة')),
                ('reason', models.CharField(choices=[('rebalancing', 'إعادة توزيع'), ('maintenance', 'صيانة المخزن'), ('capacity', 'سعة المخزن'), ('location', 'قرب الموقع'), ('emergency', 'حالة طوارئ'), ('optimization', 'تحسين التشغيل'), ('other', 'أخرى')], max_length=50, verbose_name='السبب')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.category', verbose_name='الصنف')),
                ('storage_transfer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='fuel_storage.storagetransfer', verbose_name='عملية التحويل')),
            ],
            options={
                'verbose_name': 'صنف محول',
                'verbose_name_plural': 'أصناف التحويل',
            },
        ),
        migrations.CreateModel(
            name='StorageTransferFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to='storage_transfers/', verbose_name='الملف')),
                ('file_name', models.CharField(max_length=255, verbose_name='اسم الملف')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')),
                ('storage_transfer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='files', to='fuel_storage.storagetransfer', verbose_name='عملية التحويل')),
            ],
            options={
                'verbose_name': 'ملف مرفق للتحويل',
                'verbose_name_plural': 'الملفات المرفقة للتحويل',
            },
        ),
        migrations.CreateModel(
            name='OutgoingReturnItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('returned_quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية المرتجعة')),
                ('expected_return_date', models.DateField(verbose_name='تاريخ الرد المفترض')),
                ('actual_return_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الرد الفعلي')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.category', verbose_name='الصنف')),
                ('outgoing_return', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='fuel_storage.outgoingreturn', verbose_name='مرتجع الصادر')),
            ],
            options={
                'verbose_name': 'صنف مرتجع صادر',
                'verbose_name_plural': 'أصناف مرتجع الصادر',
            },
        ),
        migrations.CreateModel(
            name='OutgoingReturnFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to='outgoing_returns/', verbose_name='الملف')),
                ('file_name', models.CharField(max_length=255, verbose_name='اسم الملف')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')),
                ('outgoing_return', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='files', to='fuel_storage.outgoingreturn', verbose_name='مرتجع الصادر')),
            ],
            options={
                'verbose_name': 'ملف مرفق لمرتجع الصادر',
                'verbose_name_plural': 'الملفات المرفقة لمرتجع الصادر',
            },
        ),
        migrations.CreateModel(
            name='OutgoingOperationItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('exported_quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية المصروفة')),
                ('transfer_date', models.DateField(verbose_name='تاريخ التحويل')),
                ('actual_transfer_date', models.DateField(blank=True, null=True, verbose_name='تاريخ التحويل الفعلي')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.category', verbose_name='الصنف')),
                ('outgoing_operation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='fuel_storage.outgoingoperation', verbose_name='عملية الصادر')),
            ],
            options={
                'verbose_name': 'صنف صادر',
                'verbose_name_plural': 'أصناف الصادر',
            },
        ),
        migrations.CreateModel(
            name='OutgoingOperationFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to='outgoing_operations/', verbose_name='الملف')),
                ('file_name', models.CharField(max_length=255, verbose_name='اسم الملف')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')),
                ('outgoing_operation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='files', to='fuel_storage.outgoingoperation', verbose_name='عملية الصادر')),
            ],
            options={
                'verbose_name': 'ملف مرفق للصادر',
                'verbose_name_plural': 'الملفات المرفقة للصادر',
            },
        ),
        migrations.AddField(
            model_name='outgoingoperation',
            name='storage',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.storage', verbose_name='المخزن'),
        ),
        migrations.CreateModel(
            name='OperationModification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('operation_type', models.CharField(choices=[('incoming', 'وارد'), ('outgoing', 'صادر')], max_length=20, verbose_name='نوع العملية')),
                ('operation_date', models.DateTimeField(verbose_name='تاريخ العملية')),
                ('previous_quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية السابقة')),
                ('new_quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية الجديدة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('modification_date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التعديل')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.category', verbose_name='الصنف')),
                ('incoming_operation', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.incomingoperation', verbose_name='عملية الوارد')),
                ('modified_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='عُدل بواسطة')),
                ('outgoing_operation', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.outgoingoperation', verbose_name='عملية الصادر')),
                ('storage', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.storage', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'تعديل عملية',
                'verbose_name_plural': 'تعديلات العمليات',
            },
        ),
        migrations.CreateModel(
            name='IncomingReturnItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('returned_quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية المرتجعة')),
                ('expected_return_date', models.DateField(verbose_name='تاريخ الرد المفترض')),
                ('actual_return_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الرد الفعلي')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.category', verbose_name='الصنف')),
                ('incoming_return', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='fuel_storage.incomingreturn', verbose_name='مرتجع الوارد')),
            ],
            options={
                'verbose_name': 'صنف مرتجع وارد',
                'verbose_name_plural': 'أصناف مرتجع الوارد',
            },
        ),
        migrations.CreateModel(
            name='IncomingReturnFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to='incoming_returns/', verbose_name='الملف')),
                ('file_name', models.CharField(max_length=255, verbose_name='اسم الملف')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')),
                ('incoming_return', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='files', to='fuel_storage.incomingreturn', verbose_name='مرتجع الوارد')),
            ],
            options={
                'verbose_name': 'ملف مرفق لمرتجع الوارد',
                'verbose_name_plural': 'الملفات المرفقة لمرتجع الوارد',
            },
        ),
        migrations.CreateModel(
            name='IncomingOperationItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('imported_quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية المستوردة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.category', verbose_name='الصنف')),
                ('incoming_operation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='fuel_storage.incomingoperation', verbose_name='عملية الوارد')),
            ],
            options={
                'verbose_name': 'صنف وارد',
                'verbose_name_plural': 'أصناف الوارد',
            },
        ),
        migrations.CreateModel(
            name='IncomingOperationFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to='incoming_operations/', verbose_name='الملف')),
                ('file_name', models.CharField(max_length=255, verbose_name='اسم الملف')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')),
                ('incoming_operation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='files', to='fuel_storage.incomingoperation', verbose_name='عملية الوارد')),
            ],
            options={
                'verbose_name': 'ملف مرفق',
                'verbose_name_plural': 'الملفات المرفقة',
            },
        ),
        migrations.AddField(
            model_name='incomingoperation',
            name='station',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.station', verbose_name='المحطة'),
        ),
        migrations.AddField(
            model_name='incomingoperation',
            name='storage',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.storage', verbose_name='المخزن'),
        ),
        migrations.AddField(
            model_name='incomingoperation',
            name='supplier',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.supplier', verbose_name='المورد'),
        ),
        migrations.CreateModel(
            name='DamageOperationItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('damaged_quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية التالفة')),
                ('reason', models.CharField(choices=[('expired', 'منتهي الصلاحية'), ('contaminated', 'ملوث'), ('leaked', 'تسرب'), ('fire', 'حريق'), ('accident', 'حادث'), ('quality_issue', 'مشكلة في الجودة'), ('other', 'أخرى')], max_length=50, verbose_name='السبب')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.category', verbose_name='الصنف')),
                ('damage_operation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='fuel_storage.damageoperation', verbose_name='عملية التلف')),
            ],
            options={
                'verbose_name': 'صنف تالف',
                'verbose_name_plural': 'أصناف التلف',
            },
        ),
        migrations.CreateModel(
            name='DamageOperationFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to='damage_operations/', verbose_name='الملف')),
                ('file_name', models.CharField(max_length=255, verbose_name='اسم الملف')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الرفع')),
                ('damage_operation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='files', to='fuel_storage.damageoperation', verbose_name='عملية التلف')),
            ],
            options={
                'verbose_name': 'ملف مرفق للتلف',
                'verbose_name_plural': 'الملفات المرفقة للتلف',
            },
        ),
        migrations.AddField(
            model_name='damageoperation',
            name='storage',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='fuel_storage.storage', verbose_name='المخزن'),
        ),
        migrations.CreateModel(
            name='StorageItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('unit_of_measure', models.CharField(choices=[('liter', 'لتر'), ('gallon', 'جالون'), ('barrel', 'برميل'), ('ton', 'طن'), ('kg', 'كيلوجرام')], max_length=20, verbose_name='وحدة القياس')),
                ('opening_balance', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='الرصيد الافتتاحي')),
                ('current_quantity', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='الكمية الحالية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.category', verbose_name='الصنف')),
                ('storage', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='fuel_storage.storage', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'صنف مخزن',
                'verbose_name_plural': 'أصناف المخازن',
                'unique_together': {('storage', 'category')},
            },
        ),
    ]
