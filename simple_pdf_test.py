#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
اختبار بسيط لـ PDF العربي
"""

from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import ParagraphStyle
from reportlab.lib.enums import TA_RIGHT, TA_CENTER
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from bidi.algorithm import get_display
import arabic_reshaper
import io
import os


def process_arabic_text_simple(text):
    """معالجة النص العربي"""
    if not text:
        return ""
    try:
        reshaped_text = arabic_reshaper.reshape(str(text))
        bidi_text = get_display(reshaped_text)
        return bidi_text
    except Exception as e:
        print(f"خطأ في معالجة النص: {e}")
        return str(text)


def register_fonts():
    """تسجيل الخطوط"""
    try:
        # محاولة استخدام خط Windows
        if os.path.exists("C:/Windows/Fonts/arial.ttf"):
            pdfmetrics.registerFont(TTFont('Arabic', "C:/Windows/Fonts/arial.ttf"))
            return True
        elif os.path.exists("C:/Windows/Fonts/tahoma.ttf"):
            pdfmetrics.registerFont(TTFont('Arabic', "C:/Windows/Fonts/tahoma.ttf"))
            return True
    except Exception as e:
        print(f"خطأ في تسجيل الخط: {e}")
    return False


def create_test_pdf():
    """إنشاء PDF تجريبي"""
    
    print("إنشاء PDF تجريبي...")
    
    # تسجيل الخطوط
    fonts_registered = register_fonts()
    font_name = 'Arabic' if fonts_registered else 'Helvetica'
    
    # إنشاء buffer
    buffer = io.BytesIO()
    
    # إنشاء المستند
    doc = SimpleDocTemplate(buffer, pagesize=A4)
    
    # إنشاء الأنماط
    title_style = ParagraphStyle(
        'ArabicTitle',
        fontName=font_name,
        fontSize=16,
        alignment=TA_CENTER,
        spaceAfter=20
    )
    
    normal_style = ParagraphStyle(
        'ArabicNormal',
        fontName=font_name,
        fontSize=12,
        alignment=TA_RIGHT,
        spaceAfter=10
    )
    
    # إنشاء المحتوى
    story = []
    
    # العنوان
    title_text = process_arabic_text_simple("تقرير اختبار PDF العربي")
    title = Paragraph(title_text, title_style)
    story.append(title)
    story.append(Spacer(1, 20))
    
    # النص
    content_text = process_arabic_text_simple("هذا نص تجريبي لاختبار عرض اللغة العربية في ملفات PDF")
    content = Paragraph(content_text, normal_style)
    story.append(content)
    story.append(Spacer(1, 15))
    
    # نص إضافي
    additional_text = process_arabic_text_simple("تم حل مشكلة عرض النصوص العربية بنجاح")
    additional = Paragraph(additional_text, normal_style)
    story.append(additional)
    
    try:
        # إنشاء PDF
        doc.build(story)
        buffer.seek(0)
        
        # حفظ الملف
        with open('arabic_test.pdf', 'wb') as f:
            f.write(buffer.getvalue())
        
        print("✅ تم إنشاء ملف PDF بنجاح: arabic_test.pdf")
        print(f"استخدام الخط: {font_name}")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء PDF: {e}")
        return False


if __name__ == "__main__":
    success = create_test_pdf()
    if success:
        print("\n🎉 تم إنشاء PDF العربي بنجاح!")
    else:
        print("\n❌ فشل في إنشاء PDF العربي")
