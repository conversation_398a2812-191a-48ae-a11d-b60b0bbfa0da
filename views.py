from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.db.models import Sum, Count, Q
from django.http import HttpResponse, JsonResponse
from django.utils import timezone
from datetime import datetime, timedelta
import json
from .models import (
    Storage, Category, Supplier, Beneficiary, 
    StorageItem, IncomingOperation, OutgoingOperation,
    OperationModification, IncomingReturn, OutgoingReturn,
    DamageOperation, StorageTransfer, IncomingOperationItem,
    OutgoingOperationItem, DamageOperationItem, StorageTransferItem
)
from .utils import generate_pdf_report, generate_excel_report

@login_required
def dashboard(request):
    # إحصائيات عامة
    total_storages = Storage.objects.filter(is_active=True).count()
    total_categories = Category.objects.filter(is_active=True).count()
    total_suppliers = Supplier.objects.filter(is_active=True).count()
    total_incoming_operations = IncomingOperation.objects.count()
    total_outgoing_operations = OutgoingOperation.objects.count()
    total_beneficiaries = Beneficiary.objects.filter(is_active=True).count()
    total_incoming_returns = IncomingReturn.objects.count()
    total_outgoing_returns = OutgoingReturn.objects.count()
    total_damage_operations = DamageOperation.objects.count()
    total_storage_transfers = StorageTransfer.objects.count()
    
    # أحدث العمليات
    recent_incoming = IncomingOperation.objects.select_related(
        'storage', 'supplier', 'station'
    ).order_by('-created_at')[:3]
    
    recent_outgoing = OutgoingOperation.objects.select_related(
        'storage', 'beneficiary'
    ).order_by('-created_at')[:3]
    
    # المخازن مع أصنافها
    storages_with_items = Storage.objects.filter(is_active=True).prefetch_related('storageitem_set__category')[:5]
    
    context = {
        'total_storages': total_storages,
        'total_categories': total_categories,
        'total_suppliers': total_suppliers,
        'total_incoming_operations': total_incoming_operations,
        'total_outgoing_operations': total_outgoing_operations,
        'total_beneficiaries': total_beneficiaries,
        'total_incoming_returns': total_incoming_returns,
        'total_outgoing_returns': total_outgoing_returns,
        'total_damage_operations': total_damage_operations,
        'total_storage_transfers': total_storage_transfers,
        'recent_incoming': recent_incoming,
        'recent_outgoing': recent_outgoing,
        'storages_with_items': storages_with_items,
    }
    
    return render(request, 'fuel_storage/dashboard.html', context)

@login_required
def storage_list(request):
    storages = Storage.objects.filter(is_active=True).order_by('name')
    return render(request, 'fuel_storage/storage_list.html', {'storages': storages})

@login_required
def category_list(request):
    categories = Category.objects.filter(is_active=True).order_by('name')
    return render(request, 'fuel_storage/category_list.html', {'categories': categories})

@login_required
def supplier_list(request):
    suppliers = Supplier.objects.filter(is_active=True).order_by('full_name')
    return render(request, 'fuel_storage/supplier_list.html', {'suppliers': suppliers})

@login_required
def incoming_operations_list(request):
    operations = IncomingOperation.objects.select_related(
        'storage', 'supplier', 'station', 'created_by'
    ).order_by('-operation_date')
    return render(request, 'fuel_storage/incoming_operations_list.html', {'operations': operations})

@login_required
def storage_items_list(request):
    items = StorageItem.objects.select_related(
        'storage', 'category'
    ).filter(storage__is_active=True, category__is_active=True).order_by('storage__name', 'category__name')
    return render(request, 'fuel_storage/storage_items_list.html', {'items': items})

@login_required
def outgoing_operations_list(request):
    operations = OutgoingOperation.objects.select_related(
        'storage', 'beneficiary', 'created_by'
    ).order_by('-operation_date')
    return render(request, 'fuel_storage/outgoing_operations_list.html', {'operations': operations})

@login_required
def beneficiary_list(request):
    beneficiaries = Beneficiary.objects.filter(is_active=True).order_by('full_name')
    return render(request, 'fuel_storage/beneficiary_list.html', {'beneficiaries': beneficiaries})

@login_required
def operation_modifications_list(request):
    modifications = OperationModification.objects.select_related(
        'storage', 'category', 'modified_by'
    ).order_by('-modification_date')
    return render(request, 'fuel_storage/operation_modifications_list.html', {'modifications': modifications})

@login_required
def storage_report(request):
    storage_id = request.GET.get('storage_id')
    storages = Storage.objects.filter(is_active=True)
    
    if storage_id:
        selected_storage = get_object_or_404(Storage, id=storage_id, is_active=True)
        storage_items = StorageItem.objects.filter(
            storage=selected_storage, 
            category__is_active=True
        ).select_related('category')
        
        # إحصائيات العمليات
        incoming_count = IncomingOperation.objects.filter(storage=selected_storage).count()
        outgoing_count = OutgoingOperation.objects.filter(storage=selected_storage).count()
        
        context = {
            'storages': storages,
            'selected_storage': selected_storage,
            'storage_items': storage_items,
            'incoming_count': incoming_count,
            'outgoing_count': outgoing_count,
        }
    else:
        context = {'storages': storages}
    
    return render(request, 'fuel_storage/storage_report.html', context)

@login_required
def incoming_returns_list(request):
    returns = IncomingReturn.objects.select_related(
        'incoming_operation', 'incoming_operation__storage', 
        'incoming_operation__supplier', 'created_by'
    ).order_by('-return_date')
    return render(request, 'fuel_storage/incoming_returns_list.html', {'returns': returns})

@login_required
def outgoing_returns_list(request):
    returns = OutgoingReturn.objects.select_related(
        'outgoing_operation', 'outgoing_operation__storage', 
        'outgoing_operation__beneficiary', 'created_by'
    ).order_by('-return_date')
    return render(request, 'fuel_storage/outgoing_returns_list.html', {'returns': returns})

@login_required
def returns_summary(request):
    # إحصائيات المرتجعات
    total_incoming_returns = IncomingReturn.objects.count()
    total_outgoing_returns = OutgoingReturn.objects.count()
    
    # أحدث المرتجعات
    recent_incoming_returns = IncomingReturn.objects.select_related(
        'incoming_operation', 'created_by'
    ).order_by('-created_at')[:5]
    
    recent_outgoing_returns = OutgoingReturn.objects.select_related(
        'outgoing_operation', 'created_by'
    ).order_by('-created_at')[:5]
    
    context = {
        'total_incoming_returns': total_incoming_returns,
        'total_outgoing_returns': total_outgoing_returns,
        'recent_incoming_returns': recent_incoming_returns,
        'recent_outgoing_returns': recent_outgoing_returns,
    }
    
    return render(request, 'fuel_storage/returns_summary.html', context)

@login_required
def damage_operations_list(request):
    operations = DamageOperation.objects.select_related(
        'storage', 'created_by'
    ).order_by('-damage_date')
    return render(request, 'fuel_storage/damage_operations_list.html', {'operations': operations})

@login_required
def storage_transfers_list(request):
    transfers = StorageTransfer.objects.select_related(
        'from_storage', 'to_storage', 'created_by'
    ).order_by('-transfer_date')
    return render(request, 'fuel_storage/storage_transfers_list.html', {'transfers': transfers})

@login_required
def operations_summary(request):
    # إحصائيات العمليات
    total_damage_operations = DamageOperation.objects.count()
    total_storage_transfers = StorageTransfer.objects.count()
    
    # أحدث العمليات
    recent_damage_operations = DamageOperation.objects.select_related(
        'storage', 'created_by'
    ).order_by('-created_at')[:5]
    
    recent_storage_transfers = StorageTransfer.objects.select_related(
        'from_storage', 'to_storage', 'created_by'
    ).order_by('-created_at')[:5]
    
    context = {
        'total_damage_operations': total_damage_operations,
        'total_storage_transfers': total_storage_transfers,
        'recent_damage_operations': recent_damage_operations,
        'recent_storage_transfers': recent_storage_transfers,
    }
    
    return render(request, 'fuel_storage/operations_summary.html', context)

# تقارير متقدمة
@login_required
def storage_movement_report(request):
    """تقرير حركة عامة للمخزن"""
    storage_id = request.GET.get('storage_id')
    from_date = request.GET.get('from_date')
    to_date = request.GET.get('to_date')
    export_format = request.GET.get('export')
    
    storages = Storage.objects.filter(is_active=True)
    context = {'storages': storages}
    
    if storage_id:
        selected_storage = get_object_or_404(Storage, id=storage_id, is_active=True)
        
        # تطبيق فلتر التاريخ
        date_filter = Q()
        if from_date:
            date_filter &= Q(operation_date__gte=from_date)
        if to_date:
            date_filter &= Q(operation_date__lte=to_date)
        
        # عمليات الوارد
        incoming_operations = IncomingOperation.objects.filter(
            storage=selected_storage
        ).filter(date_filter).select_related('supplier', 'station').prefetch_related('items__category')
        
        # عمليات الصادر
        outgoing_operations = OutgoingOperation.objects.filter(
            storage=selected_storage
        ).filter(date_filter).select_related('beneficiary').prefetch_related('items__category')
        
        # عمليات المرتجع
        incoming_returns = IncomingReturn.objects.filter(
            incoming_operation__storage=selected_storage
        ).filter(return_date__gte=from_date if from_date else timezone.now() - timedelta(days=365),
                return_date__lte=to_date if to_date else timezone.now()).prefetch_related('items__category')
        
        outgoing_returns = OutgoingReturn.objects.filter(
            outgoing_operation__storage=selected_storage
        ).filter(return_date__gte=from_date if from_date else timezone.now() - timedelta(days=365),
                return_date__lte=to_date if to_date else timezone.now()).prefetch_related('items__category')
        
        # عمليات التلف
        damage_operations = DamageOperation.objects.filter(
            storage=selected_storage
        ).filter(damage_date__gte=from_date if from_date else timezone.now() - timedelta(days=365),
                damage_date__lte=to_date if to_date else timezone.now()).prefetch_related('items__category')
        
        # عمليات التحويل
        transfer_from = StorageTransfer.objects.filter(
            from_storage=selected_storage
        ).filter(transfer_date__gte=from_date if from_date else timezone.now() - timedelta(days=365),
                transfer_date__lte=to_date if to_date else timezone.now()).prefetch_related('items__category')
        
        transfer_to = StorageTransfer.objects.filter(
            to_storage=selected_storage
        ).filter(transfer_date__gte=from_date if from_date else timezone.now() - timedelta(days=365),
                transfer_date__lte=to_date if to_date else timezone.now()).prefetch_related('items__category')
        
        context.update({
            'selected_storage': selected_storage,
            'incoming_operations': incoming_operations,
            'outgoing_operations': outgoing_operations,
            'incoming_returns': incoming_returns,
            'outgoing_returns': outgoing_returns,
            'damage_operations': damage_operations,
            'transfer_from': transfer_from,
            'transfer_to': transfer_to,
            'from_date': from_date,
            'to_date': to_date,
        })
        
        # تصدير التقرير
        if export_format in ['pdf', 'excel']:
            report_data = {
                'title': f'تقرير حركة المخزن - {selected_storage.name}',
                'storage': selected_storage,
                'data': context,
                'from_date': from_date,
                'to_date': to_date,
            }
            
            if export_format == 'pdf':
                return generate_pdf_report(report_data, 'storage_movement')
            elif export_format == 'excel':
                return generate_excel_report(report_data, 'storage_movement')
    
    return render(request, 'fuel_storage/reports/storage_movement_report.html', context)

@login_required
def storage_status_report(request):
    """تقرير حالة مخزن"""
    storage_id = request.GET.get('storage_id')
    export_format = request.GET.get('export')
    
    storages = Storage.objects.filter(is_active=True)
    context = {'storages': storages}
    
    if storage_id:
        selected_storage = get_object_or_404(Storage, id=storage_id, is_active=True)
        storage_items = StorageItem.objects.filter(
            storage=selected_storage,
            category__is_active=True
        ).select_related('category').order_by('category__name')
        
        context.update({
            'selected_storage': selected_storage,
            'storage_items': storage_items,
        })
        
        # تصدير التقرير
        if export_format in ['pdf', 'excel']:
            report_data = {
                'title': f'تقرير حالة المخزن - {selected_storage.name}',
                'storage': selected_storage,
                'items': storage_items,
            }
            
            if export_format == 'pdf':
                return generate_pdf_report(report_data, 'storage_status')
            elif export_format == 'excel':
                return generate_excel_report(report_data, 'storage_status')
    
    return render(request, 'fuel_storage/reports/storage_status_report.html', context)

@login_required
def category_movement_report(request):
    """تقرير حركة المخزن بحسب صنف محدد"""
    category_id = request.GET.get('category_id')
    from_date = request.GET.get('from_date')
    to_date = request.GET.get('to_date')
    export_format = request.GET.get('export')
    
    categories = Category.objects.filter(is_active=True)
    context = {'categories': categories}
    
    if category_id:
        selected_category = get_object_or_404(Category, id=category_id, is_active=True)
        
        # تطبيق فلتر التاريخ
        date_filter = Q()
        if from_date:
            date_filter &= Q(incoming_operation__operation_date__gte=from_date)
        if to_date:
            date_filter &= Q(incoming_operation__operation_date__lte=to_date)
        
        # عمليات الوارد للصنف
        incoming_items = IncomingOperationItem.objects.filter(
            category=selected_category
        ).filter(date_filter).select_related('incoming_operation__storage', 'incoming_operation__supplier')
        
        # عمليات الصادر للصنف
        outgoing_date_filter = Q()
        if from_date:
            outgoing_date_filter &= Q(outgoing_operation__operation_date__gte=from_date)
        if to_date:
            outgoing_date_filter &= Q(outgoing_operation__operation_date__lte=to_date)
        
        outgoing_items = OutgoingOperationItem.objects.filter(
            category=selected_category
        ).filter(outgoing_date_filter).select_related('outgoing_operation__storage', 'outgoing_operation__beneficiary')
        
        # عمليات المرتجع للصنف
        return_date_filter = Q()
        if from_date:
            return_date_filter &= Q(incoming_return__return_date__gte=from_date)
        if to_date:
            return_date_filter &= Q(incoming_return__return_date__lte=to_date)
        
        incoming_return_items = IncomingReturnItem.objects.filter(
            category=selected_category
        ).filter(return_date_filter).select_related('incoming_return__incoming_operation__storage')
        
        outgoing_return_date_filter = Q()
        if from_date:
            outgoing_return_date_filter &= Q(outgoing_return__return_date__gte=from_date)
        if to_date:
            outgoing_return_date_filter &= Q(outgoing_return__return_date__lte=to_date)
        
        outgoing_return_items = OutgoingReturnItem.objects.filter(
            category=selected_category
        ).filter(outgoing_return_date_filter).select_related('outgoing_return__outgoing_operation__storage')
        
        # عمليات التلف للصنف
        damage_date_filter = Q()
        if from_date:
            damage_date_filter &= Q(damage_operation__damage_date__gte=from_date)
        if to_date:
            damage_date_filter &= Q(damage_operation__damage_date__lte=to_date)
        
        damage_items = DamageOperationItem.objects.filter(
            category=selected_category
        ).filter(damage_date_filter).select_related('damage_operation__storage')
        
        # عمليات التحويل للصنف
        transfer_date_filter = Q()
        if from_date:
            transfer_date_filter &= Q(storage_transfer__transfer_date__gte=from_date)
        if to_date:
            transfer_date_filter &= Q(storage_transfer__transfer_date__lte=to_date)
        
        transfer_items = StorageTransferItem.objects.filter(
            category=selected_category
        ).filter(transfer_date_filter).select_related('storage_transfer__from_storage', 'storage_transfer__to_storage')
        
        context.update({
            'selected_category': selected_category,
            'incoming_items': incoming_items,
            'outgoing_items': outgoing_items,
            'incoming_return_items': incoming_return_items,
            'outgoing_return_items': outgoing_return_items,
            'damage_items': damage_items,
            'transfer_items': transfer_items,
            'from_date': from_date,
            'to_date': to_date,
        })
        
        # تصدير التقرير
        if export_format in ['pdf', 'excel']:
            report_data = {
                'title': f'تقرير حركة الصنف - {selected_category.name}',
                'category': selected_category,
                'data': context,
                'from_date': from_date,
                'to_date': to_date,
            }
            
            if export_format == 'pdf':
                return generate_pdf_report(report_data, 'category_movement')
            elif export_format == 'excel':
                return generate_excel_report(report_data, 'category_movement')
    
    return render(request, 'fuel_storage/reports/category_movement_report.html', context)

@login_required
def category_status_report(request):
    """تقرير حالة الصنف في المخزن"""
    category_id = request.GET.get('category_id')
    export_format = request.GET.get('export')
    
    categories = Category.objects.filter(is_active=True)
    context = {'categories': categories}
    
    if category_id:
        selected_category = get_object_or_404(Category, id=category_id, is_active=True)
        
        # الكميات المتوفرة من الصنف في جميع المخازن
        storage_items = StorageItem.objects.filter(
            category=selected_category,
            storage__is_active=True
        ).select_related('storage').order_by('storage__name')
        
        # حساب إجمالي الكمية
        total_quantity = storage_items.aggregate(total=Sum('current_quantity'))['total'] or 0
        
        context.update({
            'selected_category': selected_category,
            'storage_items': storage_items,
            'total_quantity': total_quantity,
        })
        
        # تصدير التقرير
        if export_format in ['pdf', 'excel']:
            report_data = {
                'title': f'تقرير حالة الصنف - {selected_category.name}',
                'category': selected_category,
                'items': storage_items,
                'total_quantity': total_quantity,
            }
            
            if export_format == 'pdf':
                return generate_pdf_report(report_data, 'category_status')
            elif export_format == 'excel':
                return generate_excel_report(report_data, 'category_status')
    
    return render(request, 'fuel_storage/reports/category_status_report.html', context)

@login_required
def beneficiary_movement_report(request):
    """تقرير حركة الصادر بحسب مستفيد محدد"""
    beneficiary_id = request.GET.get('beneficiary_id')
    from_date = request.GET.get('from_date')
    to_date = request.GET.get('to_date')
    export_format = request.GET.get('export')
    
    beneficiaries = Beneficiary.objects.filter(is_active=True)
    context = {'beneficiaries': beneficiaries}
    
    if beneficiary_id:
        selected_beneficiary = get_object_or_404(Beneficiary, id=beneficiary_id, is_active=True)
        
        # تطبيق فلتر التاريخ
        date_filter = Q()
        if from_date:
            date_filter &= Q(operation_date__gte=from_date)
        if to_date:
            date_filter &= Q(operation_date__lte=to_date)
        
        # عمليات الصادر للمستفيد
        outgoing_operations = OutgoingOperation.objects.filter(
            beneficiary=selected_beneficiary
        ).filter(date_filter).select_related('storage').prefetch_related('items__category')
        
        # حساب إجمالي الكميات
        total_quantities = {}
        for operation in outgoing_operations:
            for item in operation.items.all():
                category_name = item.category.name
                if category_name not in total_quantities:
                    total_quantities[category_name] = 0
                total_quantities[category_name] += item.exported_quantity
        
        context.update({
            'selected_beneficiary': selected_beneficiary,
            'outgoing_operations': outgoing_operations,
            'total_quantities': total_quantities,
            'from_date': from_date,
            'to_date': to_date,
        })
        
        # تصدير التقرير
        if export_format in ['pdf', 'excel']:
            report_data = {
                'title': f'تقرير حركة المستفيد - {selected_beneficiary.full_name}',
                'beneficiary': selected_beneficiary,
                'operations': outgoing_operations,
                'total_quantities': total_quantities,
                'from_date': from_date,
                'to_date': to_date,
            }
            
            if export_format == 'pdf':
                return generate_pdf_report(report_data, 'beneficiary_movement')
            elif export_format == 'excel':
                return generate_excel_report(report_data, 'beneficiary_movement')
    
    return render(request, 'fuel_storage/reports/beneficiary_movement_report.html', context)

@login_required
def supplier_movement_report(request):
    """تقرير حركة الوارد بحسب مورد أو محطة محددة"""
    supplier_id = request.GET.get('supplier_id')
    station_id = request.GET.get('station_id')
    from_date = request.GET.get('from_date')
    to_date = request.GET.get('to_date')
    export_format = request.GET.get('export')
    
    suppliers = Supplier.objects.filter(is_active=True)
    stations = Station.objects.filter(is_active=True)
    context = {'suppliers': suppliers, 'stations': stations}
    
    if supplier_id or station_id:
        # تطبيق فلتر التاريخ
        date_filter = Q()
        if from_date:
            date_filter &= Q(operation_date__gte=from_date)
        if to_date:
            date_filter &= Q(operation_date__lte=to_date)
        
        # فلتر حسب المورد أو المحطة
        operation_filter = Q()
        selected_supplier = None
        selected_station = None
        
        if supplier_id:
            selected_supplier = get_object_or_404(Supplier, id=supplier_id, is_active=True)
            operation_filter &= Q(supplier=selected_supplier)
        
        if station_id:
            selected_station = get_object_or_404(Station, id=station_id, is_active=True)
            operation_filter &= Q(station=selected_station)
        
        # عمليات الوارد
        incoming_operations = IncomingOperation.objects.filter(
            operation_filter
        ).filter(date_filter).select_related('storage', 'supplier', 'station').prefetch_related('items__category')
        
        # حساب إجمالي الكميات
        total_quantities = {}
        for operation in incoming_operations:
            for item in operation.items.all():
                category_name = item.category.name
                if category_name not in total_quantities:
                    total_quantities[category_name] = 0
                total_quantities[category_name] += item.imported_quantity
        
        context.update({
            'selected_supplier': selected_supplier,
            'selected_station': selected_station,
            'incoming_operations': incoming_operations,
            'total_quantities': total_quantities,
            'from_date': from_date,
            'to_date': to_date,
        })
        
        # تصدير التقرير
        if export_format in ['pdf', 'excel']:
            title = 'تقرير حركة الوارد - '
            if selected_supplier:
                title += selected_supplier.full_name
            if selected_station:
                title += f" - {selected_station.name}"
            
            report_data = {
                'title': title,
                'supplier': selected_supplier,
                'station': selected_station,
                'operations': incoming_operations,
                'total_quantities': total_quantities,
                'from_date': from_date,
                'to_date': to_date,
            }
            
            if export_format == 'pdf':
                return generate_pdf_report(report_data, 'supplier_movement')
            elif export_format == 'excel':
                return generate_excel_report(report_data, 'supplier_movement')
    
    return render(request, 'fuel_storage/reports/supplier_movement_report.html', context)

@login_required
def reports_dashboard(request):
    """لوحة تحكم التقارير"""
    return render(request, 'fuel_storage/reports/reports_dashboard.html')
