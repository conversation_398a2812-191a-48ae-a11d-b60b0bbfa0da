{% extends 'fuel_storage/crud/base_form.html' %}

{% block breadcrumb %}
    <li class="breadcrumb-item"><a href="{% url 'fuel_storage:storage_list_crud' %}">المخازن</a></li>
{% endblock %}

{% block back_url %}{% url 'fuel_storage:storage_list_crud' %}{% endblock %}

{% block form_content %}
<div class="row">
    <!-- اسم المخزن -->
    <div class="col-md-6 mb-3">
        <label for="{{ form.name.id_for_label }}" class="form-label">
            {{ form.name.label }}
            <span class="text-danger">*</span>
        </label>
        {{ form.name }}
        {% if form.name.help_text %}
            <div class="form-text">{{ form.name.help_text }}</div>
        {% endif %}
        {% if form.name.errors %}
            <div class="invalid-feedback d-block">
                {% for error in form.name.errors %}
                    {{ error }}
                {% endfor %}
            </div>
        {% endif %}
    </div>

    <!-- تصنيف المخزن -->
    <div class="col-md-6 mb-3">
        <label for="{{ form.classification.id_for_label }}" class="form-label">
            {{ form.classification.label }}
            <span class="text-danger">*</span>
        </label>
        {{ form.classification }}
        {% if form.classification.help_text %}
            <div class="form-text">{{ form.classification.help_text }}</div>
        {% endif %}
        {% if form.classification.errors %}
            <div class="invalid-feedback d-block">
                {% for error in form.classification.errors %}
                    {{ error }}
                {% endfor %}
            </div>
        {% endif %}
    </div>

    <!-- اسم أمين المخزن -->
    <div class="col-md-6 mb-3">
        <label for="{{ form.keeper_name.id_for_label }}" class="form-label">
            {{ form.keeper_name.label }}
            <span class="text-danger">*</span>
        </label>
        {{ form.keeper_name }}
        {% if form.keeper_name.help_text %}
            <div class="form-text">{{ form.keeper_name.help_text }}</div>
        {% endif %}
        {% if form.keeper_name.errors %}
            <div class="invalid-feedback d-block">
                {% for error in form.keeper_name.errors %}
                    {{ error }}
                {% endfor %}
            </div>
        {% endif %}
    </div>

    <!-- رقم الهاتف -->
    <div class="col-md-6 mb-3">
        <label for="{{ form.phone_number.id_for_label }}" class="form-label">
            {{ form.phone_number.label }}
            <span class="text-danger">*</span>
        </label>
        {{ form.phone_number }}
        {% if form.phone_number.help_text %}
            <div class="form-text">{{ form.phone_number.help_text }}</div>
        {% endif %}
        {% if form.phone_number.errors %}
            <div class="invalid-feedback d-block">
                {% for error in form.phone_number.errors %}
                    {{ error }}
                {% endfor %}
            </div>
        {% endif %}
    </div>

    <!-- حالة النشاط -->
    <div class="col-12 mb-3">
        <div class="form-check">
            {{ form.is_active }}
            <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                {{ form.is_active.label }}
            </label>
        </div>
        {% if form.is_active.help_text %}
            <div class="form-text">{{ form.is_active.help_text }}</div>
        {% endif %}
        {% if form.is_active.errors %}
            <div class="invalid-feedback d-block">
                {% for error in form.is_active.errors %}
                    {{ error }}
                {% endfor %}
            </div>
        {% endif %}
    </div>
</div>

<!-- معلومات إضافية -->
{% if storage %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card bg-light">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>معلومات إضافية
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p class="mb-2">
                            <strong>تاريخ الإنشاء:</strong> 
                            {{ storage.created_at|date:"Y-m-d H:i" }}
                        </p>
                        <p class="mb-2">
                            <strong>عدد الأصناف:</strong> 
                            {{ storage.storage_items.count }} صنف
                        </p>
                    </div>
                    <div class="col-md-6">
                        <p class="mb-2">
                            <strong>عمليات الوارد:</strong> 
                            {{ storage.incoming_operations.count }} عملية
                        </p>
                        <p class="mb-2">
                            <strong>عمليات الصادر:</strong> 
                            {{ storage.outgoing_operations.count }} عملية
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block header_buttons %}
<div class="btn-group">
    <a href="{% url 'fuel_storage:storage_list_crud' %}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
    </a>
    {% if storage %}
        <a href="{% url 'fuel_storage:storage_report' %}?storage_id={{ storage.id }}" class="btn btn-outline-info">
            <i class="fas fa-chart-bar me-2"></i>عرض التقرير
        </a>
    {% endif %}
</div>
{% endblock %}
