{% extends 'base.html' %}

{% block title %}عمليات التلف - نظام إدارة مخازن المحروقات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">عمليات التلف</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="/admin/fuel_storage/damageoperation/add/" class="btn btn-sm btn-outline-secondary">
            <i class="fas fa-plus me-1"></i>
            إضافة عملية تلف جديدة
        </a>
    </div>
</div>

{% if operations %}
    <div class="table-responsive">
        <table class="table table-striped table-sm">
            <thead>
                <tr>
                    <th>الرقم الورقي</th>
                    <th>المخزن</th>
                    <th>تاريخ التلف</th>
                    <th>المسلم</th>
                    <th>المستلم</th>
                    <th>البيان</th>
                    <th>أنشئ بواسطة</th>
                    <th>تاريخ الإنشاء</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for operation in operations %}
                <tr>
                    <td>
                        <span class="badge bg-danger">{{ operation.paper_number }}</span>
                    </td>
                    <td>{{ operation.storage.name }}</td>
                    <td>{{ operation.damage_date|date:"Y-m-d H:i" }}</td>
                    <td>
                        <div>{{ operation.deliverer_name }}</div>
                        <small class="text-muted">{{ operation.deliverer_job_number }}</small>
                    </td>
                    <td>
                        <div>{{ operation.receiver_name }}</div>
                        <small class="text-muted">{{ operation.receiver_job_number }}</small>
                    </td>
                    <td>
                        <span class="text-truncate" style="max-width: 200px; display: inline-block;" title="{{ operation.statement }}">
                            {{ operation.statement|truncatechars:50 }}
                        </span>
                    </td>
                    <td>{{ operation.created_by.full_name|default:operation.created_by.username }}</td>
                    <td>{{ operation.created_at|date:"Y-m-d H:i" }}</td>
                    <td>
                        <a href="/admin/fuel_storage/damageoperation/{{ operation.id }}/change/" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-edit"></i>
                        </a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
{% else %}
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        لا توجد عمليات تلف مسجلة حتى الآن.
    </div>
{% endif %}

<!-- إحصائيات سريعة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-danger">معلومات مهمة حول عمليات التلف</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تنبيه:</strong> عمليات التلف تؤدي إلى خصم الكميات نهائياً من المخزون. يرجى التأكد من صحة البيانات قبل الحفظ.
                </div>
                <ul class="list-unstyled mb-0">
                    <li><i class="fas fa-check text-success me-2"></i>يتم خصم الكميات التالفة تلقائياً من المخزن المحدد</li>
                    <li><i class="fas fa-check text-success me-2"></i>يمكن إرفاق ملفات داعمة مع كل عملية تلف</li>
                    <li><i class="fas fa-check text-success me-2"></i>يتم تسجيل أسباب التلف لكل صنف</li>
                    <li><i class="fas fa-check text-success me-2"></i>جميع العمليات مؤرخة ومرتبطة بالمستخدم المنشئ</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
