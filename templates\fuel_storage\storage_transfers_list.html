{% extends 'base.html' %}

{% block title %}التحويل المخزني - نظام إدارة مخازن المحروقات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
    <h1 class="h2">عمليات التحويل المخزني</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="/admin/fuel_storage/storagetransfer/add/" class="btn btn-sm btn-outline-secondary">
            <i class="fas fa-plus me-1"></i>
            إضافة عملية تحويل جديدة
        </a>
    </div>
</div>

{% if transfers %}
    <div class="table-responsive">
        <table class="table table-striped table-sm">
            <thead>
                <tr>
                    <th>الرقم الورقي</th>
                    <th>من المخزن</th>
                    <th>إلى المخزن</th>
                    <th>تاريخ النقل</th>
                    <th>المسلم</th>
                    <th>المستلم</th>
                    <th>البيان</th>
                    <th>أنشئ بواسطة</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for transfer in transfers %}
                <tr>
                    <td>
                        <span class="badge bg-primary">{{ transfer.paper_number }}</span>
                    </td>
                    <td>
                        <span class="badge bg-warning text-dark">{{ transfer.from_storage.name }}</span>
                    </td>
                    <td>
                        <span class="badge bg-success">{{ transfer.to_storage.name }}</span>
                    </td>
                    <td>{{ transfer.transfer_date|date:"Y-m-d H:i" }}</td>
                    <td>
                        <div>{{ transfer.deliverer_name }}</div>
                        <small class="text-muted">{{ transfer.deliverer_job_number }}</small>
                    </td>
                    <td>
                        <div>{{ transfer.receiver_name }}</div>
                        <small class="text-muted">{{ transfer.receiver_job_number }}</small>
                    </td>
                    <td>
                        <span class="text-truncate" style="max-width: 200px; display: inline-block;" title="{{ transfer.statement }}">
                            {{ transfer.statement|truncatechars:50 }}
                        </span>
                    </td>
                    <td>{{ transfer.created_by.full_name|default:transfer.created_by.username }}</td>
                    <td>
                        <a href="/admin/fuel_storage/storagetransfer/{{ transfer.id }}/change/" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-edit"></i>
                        </a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
{% else %}
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        لا توجد عمليات تحويل مخزني مسجلة حتى الآن.
    </div>
{% endif %}

<!-- إحصائيات سريعة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">معلومات حول عمليات التحويل المخزني</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>معلومة:</strong> عمليات التحويل تنقل الكميات من مخزن إلى آخر دون فقدان في الكمية الإجمالية.
                </div>
                <ul class="list-unstyled mb-0">
                    <li><i class="fas fa-check text-success me-2"></i>يتم خصم الكميات من المخزن المصدر وإضافتها للمخزن الهدف</li>
                    <li><i class="fas fa-check text-success me-2"></i>التحقق التلقائي من توفر الكميات قبل التحويل</li>
                    <li><i class="fas fa-check text-success me-2"></i>منع التحويل من المخزن إلى نفسه</li>
                    <li><i class="fas fa-check text-success me-2"></i>تسجيل أسباب التحويل لكل صنف</li>
                    <li><i class="fas fa-check text-success me-2"></i>إمكانية إرفاق ملفات داعمة</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
