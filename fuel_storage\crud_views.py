# -*- coding: utf-8 -*-
"""
Views للعمليات CRUD المخصصة
"""

from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
import json

from .models import (
    Storage, Category, Supplier, Beneficiary, Station,
    StorageItem, IncomingOperation, IncomingOperationItem,
    OutgoingOperation, OutgoingOperationItem, OperationModification
)
from .forms import (
    StorageForm, CategoryForm, SupplierForm, BeneficiaryForm, StationForm,
    StorageItemForm, IncomingOperationForm, IncomingOperationItemForm,
    OutgoingOperationForm, OutgoingOperationItemForm, OperationModificationForm,
    SearchForm, StorageFilterForm
)


# ===== دوال مساعدة =====

def paginate_queryset(request, queryset, per_page=20):
    """تقسيم النتائج إلى صفحات"""
    paginator = Paginator(queryset, per_page)
    page_number = request.GET.get('page')
    return paginator.get_page(page_number)


def handle_search(request, queryset, search_fields):
    """معالجة البحث في الحقول المحددة"""
    search_query = request.GET.get('search', '').strip()
    if search_query:
        search_filter = Q()
        for field in search_fields:
            search_filter |= Q(**{f"{field}__icontains": search_query})
        queryset = queryset.filter(search_filter)
    return queryset


# ===== إدارة المخازن =====

@login_required
def storage_list_view(request):
    """عرض قائمة المخازن مع البحث والتصفية"""
    storages = Storage.objects.all().order_by('-created_at')
    
    # البحث
    storages = handle_search(request, storages, ['name', 'keeper_name'])
    
    # التصفية
    classification = request.GET.get('classification')
    if classification:
        storages = storages.filter(classification=classification)
    
    is_active = request.GET.get('is_active')
    if is_active == 'true':
        storages = storages.filter(is_active=True)
    elif is_active == 'false':
        storages = storages.filter(is_active=False)
    
    # التقسيم إلى صفحات
    page_obj = paginate_queryset(request, storages)
    
    # نموذج التصفية
    filter_form = StorageFilterForm(request.GET)
    
    context = {
        'storages': page_obj,
        'filter_form': filter_form,
        'search_query': request.GET.get('search', ''),
        'total_count': storages.count(),
    }
    
    return render(request, 'fuel_storage/crud/storage_list.html', context)


@login_required
def storage_create_view(request):
    """إضافة مخزن جديد"""
    if request.method == 'POST':
        form = StorageForm(request.POST)
        if form.is_valid():
            storage = form.save()
            messages.success(request, f'تم إضافة المخزن "{storage.name}" بنجاح')
            return redirect('fuel_storage:storage_list_crud')
    else:
        form = StorageForm()
    
    context = {
        'form': form,
        'title': 'إضافة مخزن جديد',
        'submit_text': 'إضافة المخزن',
    }
    
    return render(request, 'fuel_storage/crud/storage_form.html', context)


@login_required
def storage_update_view(request, pk):
    """تعديل مخزن موجود"""
    storage = get_object_or_404(Storage, pk=pk)
    
    if request.method == 'POST':
        form = StorageForm(request.POST, instance=storage)
        if form.is_valid():
            storage = form.save()
            messages.success(request, f'تم تحديث المخزن "{storage.name}" بنجاح')
            return redirect('fuel_storage:storage_list_crud')
    else:
        form = StorageForm(instance=storage)
    
    context = {
        'form': form,
        'storage': storage,
        'title': f'تعديل المخزن: {storage.name}',
        'submit_text': 'حفظ التغييرات',
    }
    
    return render(request, 'fuel_storage/crud/storage_form.html', context)


@login_required
def storage_delete_view(request, pk):
    """حذف مخزن"""
    storage = get_object_or_404(Storage, pk=pk)
    
    if request.method == 'POST':
        try:
            storage_name = storage.name
            storage.delete()
            messages.success(request, f'تم حذف المخزن "{storage_name}" بنجاح')
        except Exception as e:
            messages.error(request, f'لا يمكن حذف المخزن: {str(e)}')
        
        return redirect('fuel_storage:storage_list_crud')
    
    # عرض صفحة التأكيد
    context = {
        'storage': storage,
        'title': f'حذف المخزن: {storage.name}',
    }
    
    return render(request, 'fuel_storage/crud/storage_confirm_delete.html', context)


# ===== إدارة الأصناف =====

@login_required
def category_list_view(request):
    """عرض قائمة الأصناف"""
    categories = Category.objects.all().order_by('name')
    
    # البحث
    categories = handle_search(request, categories, ['name'])
    
    # التقسيم إلى صفحات
    page_obj = paginate_queryset(request, categories)
    
    context = {
        'categories': page_obj,
        'search_query': request.GET.get('search', ''),
        'total_count': categories.count(),
    }
    
    return render(request, 'fuel_storage/crud/category_list.html', context)


@login_required
def category_create_view(request):
    """إضافة صنف جديد"""
    if request.method == 'POST':
        form = CategoryForm(request.POST)
        if form.is_valid():
            category = form.save()
            messages.success(request, f'تم إضافة الصنف "{category.name}" بنجاح')
            return redirect('fuel_storage:category_list_crud')
    else:
        form = CategoryForm()
    
    context = {
        'form': form,
        'title': 'إضافة صنف جديد',
        'submit_text': 'إضافة الصنف',
    }
    
    return render(request, 'fuel_storage/crud/category_form.html', context)


@login_required
def category_update_view(request, pk):
    """تعديل صنف موجود"""
    category = get_object_or_404(Category, pk=pk)
    
    if request.method == 'POST':
        form = CategoryForm(request.POST, instance=category)
        if form.is_valid():
            category = form.save()
            messages.success(request, f'تم تحديث الصنف "{category.name}" بنجاح')
            return redirect('fuel_storage:category_list_crud')
    else:
        form = CategoryForm(instance=category)
    
    context = {
        'form': form,
        'category': category,
        'title': f'تعديل الصنف: {category.name}',
        'submit_text': 'حفظ التغييرات',
    }
    
    return render(request, 'fuel_storage/crud/category_form.html', context)


@login_required
def category_delete_view(request, pk):
    """حذف صنف"""
    category = get_object_or_404(Category, pk=pk)
    
    if request.method == 'POST':
        try:
            category_name = category.name
            category.delete()
            messages.success(request, f'تم حذف الصنف "{category_name}" بنجاح')
        except Exception as e:
            messages.error(request, f'لا يمكن حذف الصنف: {str(e)}')
        
        return redirect('fuel_storage:category_list_crud')
    
    context = {
        'category': category,
        'title': f'حذف الصنف: {category.name}',
    }
    
    return render(request, 'fuel_storage/crud/category_confirm_delete.html', context)


# ===== إدارة الموردين =====

@login_required
def supplier_list_view(request):
    """عرض قائمة الموردين"""
    suppliers = Supplier.objects.all().order_by('full_name')
    
    # البحث
    suppliers = handle_search(request, suppliers, ['full_name', 'phone_number'])
    
    # التقسيم إلى صفحات
    page_obj = paginate_queryset(request, suppliers)
    
    context = {
        'suppliers': page_obj,
        'search_query': request.GET.get('search', ''),
        'total_count': suppliers.count(),
    }
    
    return render(request, 'fuel_storage/crud/supplier_list.html', context)


@login_required
def supplier_create_view(request):
    """إضافة مورد جديد"""
    if request.method == 'POST':
        form = SupplierForm(request.POST)
        if form.is_valid():
            supplier = form.save()
            messages.success(request, f'تم إضافة المورد "{supplier.full_name}" بنجاح')
            return redirect('fuel_storage:supplier_list_crud')
    else:
        form = SupplierForm()
    
    context = {
        'form': form,
        'title': 'إضافة مورد جديد',
        'submit_text': 'إضافة المورد',
    }
    
    return render(request, 'fuel_storage/crud/supplier_form.html', context)


@login_required
def supplier_update_view(request, pk):
    """تعديل مورد موجود"""
    supplier = get_object_or_404(Supplier, pk=pk)
    
    if request.method == 'POST':
        form = SupplierForm(request.POST, instance=supplier)
        if form.is_valid():
            supplier = form.save()
            messages.success(request, f'تم تحديث المورد "{supplier.full_name}" بنجاح')
            return redirect('fuel_storage:supplier_list_crud')
    else:
        form = SupplierForm(instance=supplier)
    
    context = {
        'form': form,
        'supplier': supplier,
        'title': f'تعديل المورد: {supplier.full_name}',
        'submit_text': 'حفظ التغييرات',
    }
    
    return render(request, 'fuel_storage/crud/supplier_form.html', context)


@login_required
def supplier_delete_view(request, pk):
    """حذف مورد"""
    supplier = get_object_or_404(Supplier, pk=pk)
    
    if request.method == 'POST':
        try:
            supplier_name = supplier.full_name
            supplier.delete()
            messages.success(request, f'تم حذف المورد "{supplier_name}" بنجاح')
        except Exception as e:
            messages.error(request, f'لا يمكن حذف المورد: {str(e)}')
        
        return redirect('fuel_storage:supplier_list_crud')
    
    context = {
        'supplier': supplier,
        'title': f'حذف المورد: {supplier.full_name}',
    }
    
    return render(request, 'fuel_storage/crud/supplier_confirm_delete.html', context)


# ===== إدارة المستفيدين =====

@login_required
def beneficiary_list_view(request):
    """عرض قائمة المستفيدين"""
    beneficiaries = Beneficiary.objects.all().order_by('full_name')
    
    # البحث
    beneficiaries = handle_search(request, beneficiaries, ['full_name', 'phone_number'])
    
    # التقسيم إلى صفحات
    page_obj = paginate_queryset(request, beneficiaries)
    
    context = {
        'beneficiaries': page_obj,
        'search_query': request.GET.get('search', ''),
        'total_count': beneficiaries.count(),
    }
    
    return render(request, 'fuel_storage/crud/beneficiary_list.html', context)


@login_required
def beneficiary_create_view(request):
    """إضافة مستفيد جديد"""
    if request.method == 'POST':
        form = BeneficiaryForm(request.POST)
        if form.is_valid():
            beneficiary = form.save()
            messages.success(request, f'تم إضافة المستفيد "{beneficiary.full_name}" بنجاح')
            return redirect('fuel_storage:beneficiary_list_crud')
    else:
        form = BeneficiaryForm()
    
    context = {
        'form': form,
        'title': 'إضافة مستفيد جديد',
        'submit_text': 'إضافة المستفيد',
    }
    
    return render(request, 'fuel_storage/crud/beneficiary_form.html', context)


@login_required
def beneficiary_update_view(request, pk):
    """تعديل مستفيد موجود"""
    beneficiary = get_object_or_404(Beneficiary, pk=pk)

    if request.method == 'POST':
        form = BeneficiaryForm(request.POST, instance=beneficiary)
        if form.is_valid():
            beneficiary = form.save()
            messages.success(request, f'تم تحديث المستفيد "{beneficiary.full_name}" بنجاح')
            return redirect('fuel_storage:beneficiary_list_crud')
    else:
        form = BeneficiaryForm(instance=beneficiary)

    context = {
        'form': form,
        'beneficiary': beneficiary,
        'title': f'تعديل المستفيد: {beneficiary.full_name}',
        'submit_text': 'حفظ التغييرات',
    }

    return render(request, 'fuel_storage/crud/beneficiary_form.html', context)


@login_required
def beneficiary_delete_view(request, pk):
    """حذف مستفيد"""
    beneficiary = get_object_or_404(Beneficiary, pk=pk)

    if request.method == 'POST':
        try:
            beneficiary_name = beneficiary.full_name
            beneficiary.delete()
            messages.success(request, f'تم حذف المستفيد "{beneficiary_name}" بنجاح')
        except Exception as e:
            messages.error(request, f'لا يمكن حذف المستفيد: {str(e)}')

        return redirect('fuel_storage:beneficiary_list_crud')

    context = {
        'beneficiary': beneficiary,
        'title': f'حذف المستفيد: {beneficiary.full_name}',
    }

    return render(request, 'fuel_storage/crud/beneficiary_confirm_delete.html', context)


# ===== إدارة عمليات الوارد =====

@login_required
def incoming_operation_list_view(request):
    """عرض قائمة عمليات الوارد"""
    operations = IncomingOperation.objects.all().order_by('-operation_date', '-created_at')

    # البحث
    operations = handle_search(request, operations, ['paper_number', 'supplier__full_name', 'deliverer_name'])

    # التصفية بالتاريخ
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')

    if date_from:
        operations = operations.filter(operation_date__gte=date_from)
    if date_to:
        operations = operations.filter(operation_date__lte=date_to)

    # التصفية بالمخزن
    storage_id = request.GET.get('storage')
    if storage_id:
        operations = operations.filter(storage_id=storage_id)

    # التقسيم إلى صفحات
    page_obj = paginate_queryset(request, operations)

    context = {
        'operations': page_obj,
        'search_query': request.GET.get('search', ''),
        'storages': Storage.objects.filter(is_active=True),
        'total_count': operations.count(),
    }

    return render(request, 'fuel_storage/crud/incoming_operation_list.html', context)


@login_required
def incoming_operation_create_view(request):
    """إضافة عملية وارد جديدة"""
    if request.method == 'POST':
        form = IncomingOperationForm(request.POST)
        if form.is_valid():
            operation = form.save(commit=False)
            operation.created_by = request.user
            operation.save()
            messages.success(request, f'تم إضافة عملية الوارد رقم "{operation.paper_number}" بنجاح')
            return redirect('fuel_storage:incoming_operation_detail_crud', pk=operation.pk)
    else:
        form = IncomingOperationForm()

    context = {
        'form': form,
        'title': 'إضافة عملية وارد جديدة',
        'submit_text': 'إضافة العملية',
    }

    return render(request, 'fuel_storage/crud/incoming_operation_form.html', context)


@login_required
def incoming_operation_update_view(request, pk):
    """تعديل عملية وارد موجودة"""
    operation = get_object_or_404(IncomingOperation, pk=pk)

    if request.method == 'POST':
        form = IncomingOperationForm(request.POST, instance=operation)
        if form.is_valid():
            operation = form.save()
            messages.success(request, f'تم تحديث عملية الوارد رقم "{operation.paper_number}" بنجاح')
            return redirect('fuel_storage:incoming_operation_detail_crud', pk=operation.pk)
    else:
        form = IncomingOperationForm(instance=operation)

    context = {
        'form': form,
        'operation': operation,
        'title': f'تعديل عملية الوارد: {operation.paper_number}',
        'submit_text': 'حفظ التغييرات',
    }

    return render(request, 'fuel_storage/crud/incoming_operation_form.html', context)


@login_required
def incoming_operation_detail_view(request, pk):
    """عرض تفاصيل عملية الوارد"""
    operation = get_object_or_404(IncomingOperation, pk=pk)
    items = operation.items.all()

    context = {
        'operation': operation,
        'items': items,
        'title': f'تفاصيل عملية الوارد: {operation.paper_number}',
    }

    return render(request, 'fuel_storage/crud/incoming_operation_detail.html', context)


@login_required
def incoming_operation_delete_view(request, pk):
    """حذف عملية وارد"""
    operation = get_object_or_404(IncomingOperation, pk=pk)

    if request.method == 'POST':
        try:
            operation_number = operation.paper_number
            operation.delete()
            messages.success(request, f'تم حذف عملية الوارد رقم "{operation_number}" بنجاح')
        except Exception as e:
            messages.error(request, f'لا يمكن حذف العملية: {str(e)}')

        return redirect('fuel_storage:incoming_operation_list_crud')

    context = {
        'operation': operation,
        'title': f'حذف عملية الوارد: {operation.paper_number}',
    }

    return render(request, 'fuel_storage/crud/incoming_operation_confirm_delete.html', context)


# ===== إدارة عمليات الصادر =====

@login_required
def outgoing_operation_list_view(request):
    """عرض قائمة عمليات الصادر"""
    operations = OutgoingOperation.objects.all().order_by('-operation_date', '-created_at')

    # البحث
    operations = handle_search(request, operations, ['paper_number', 'beneficiary__full_name', 'deliverer_name'])

    # التصفية بالتاريخ
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')

    if date_from:
        operations = operations.filter(operation_date__gte=date_from)
    if date_to:
        operations = operations.filter(operation_date__lte=date_to)

    # التصفية بالمخزن
    storage_id = request.GET.get('storage')
    if storage_id:
        operations = operations.filter(storage_id=storage_id)

    # التقسيم إلى صفحات
    page_obj = paginate_queryset(request, operations)

    context = {
        'operations': page_obj,
        'search_query': request.GET.get('search', ''),
        'storages': Storage.objects.filter(is_active=True),
        'total_count': operations.count(),
    }

    return render(request, 'fuel_storage/crud/outgoing_operation_list.html', context)


@login_required
def outgoing_operation_create_view(request):
    """إضافة عملية صادر جديدة"""
    if request.method == 'POST':
        form = OutgoingOperationForm(request.POST)
        if form.is_valid():
            operation = form.save(commit=False)
            operation.created_by = request.user
            operation.save()
            messages.success(request, f'تم إضافة عملية الصادر رقم "{operation.paper_number}" بنجاح')
            return redirect('fuel_storage:outgoing_operation_detail_crud', pk=operation.pk)
    else:
        form = OutgoingOperationForm()

    context = {
        'form': form,
        'title': 'إضافة عملية صادر جديدة',
        'submit_text': 'إضافة العملية',
    }

    return render(request, 'fuel_storage/crud/outgoing_operation_form.html', context)


@login_required
def outgoing_operation_update_view(request, pk):
    """تعديل عملية صادر موجودة"""
    operation = get_object_or_404(OutgoingOperation, pk=pk)

    if request.method == 'POST':
        form = OutgoingOperationForm(request.POST, instance=operation)
        if form.is_valid():
            operation = form.save()
            messages.success(request, f'تم تحديث عملية الصادر رقم "{operation.paper_number}" بنجاح')
            return redirect('fuel_storage:outgoing_operation_detail_crud', pk=operation.pk)
    else:
        form = OutgoingOperationForm(instance=operation)

    context = {
        'form': form,
        'operation': operation,
        'title': f'تعديل عملية الصادر: {operation.paper_number}',
        'submit_text': 'حفظ التغييرات',
    }

    return render(request, 'fuel_storage/crud/outgoing_operation_form.html', context)


@login_required
def outgoing_operation_detail_view(request, pk):
    """عرض تفاصيل عملية الصادر"""
    operation = get_object_or_404(OutgoingOperation, pk=pk)
    items = operation.items.all()

    context = {
        'operation': operation,
        'items': items,
        'title': f'تفاصيل عملية الصادر: {operation.paper_number}',
    }

    return render(request, 'fuel_storage/crud/outgoing_operation_detail.html', context)


@login_required
def outgoing_operation_delete_view(request, pk):
    """حذف عملية صادر"""
    operation = get_object_or_404(OutgoingOperation, pk=pk)

    if request.method == 'POST':
        try:
            operation_number = operation.paper_number
            operation.delete()
            messages.success(request, f'تم حذف عملية الصادر رقم "{operation_number}" بنجاح')
        except Exception as e:
            messages.error(request, f'لا يمكن حذف العملية: {str(e)}')

        return redirect('fuel_storage:outgoing_operation_list_crud')

    context = {
        'operation': operation,
        'title': f'حذف عملية الصادر: {operation.paper_number}',
    }

    return render(request, 'fuel_storage/crud/outgoing_operation_confirm_delete.html', context)
