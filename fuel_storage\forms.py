# -*- coding: utf-8 -*-
"""
نماذج Django للعمليات المخصصة
"""

from django import forms
from django.core.exceptions import ValidationError
from django.utils import timezone
from .models import (
    Storage, Category, Supplier, Beneficiary, Station,
    StorageItem, IncomingOperation, IncomingOperationItem,
    OutgoingOperation, OutgoingOperationItem, OperationModification,
    IncomingReturn, IncomingReturnItem, OutgoingReturn, OutgoingReturnItem,
    DamageOperation, DamageOperationItem, StorageTransfer, StorageTransferItem
)


class BaseModelForm(forms.ModelForm):
    """نموذج أساسي مع تنسيق موحد للحقول"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for field_name, field in self.fields.items():
            # إضافة CSS classes للتنسيق
            if isinstance(field.widget, forms.TextInput):
                field.widget.attrs.update({'class': 'form-control'})
            elif isinstance(field.widget, forms.Textarea):
                field.widget.attrs.update({'class': 'form-control', 'rows': 3})
            elif isinstance(field.widget, forms.Select):
                field.widget.attrs.update({'class': 'form-select'})
            elif isinstance(field.widget, forms.NumberInput):
                field.widget.attrs.update({'class': 'form-control'})
            elif isinstance(field.widget, forms.DateInput):
                field.widget.attrs.update({'class': 'form-control', 'type': 'date'})
            elif isinstance(field.widget, forms.DateTimeInput):
                field.widget.attrs.update({'class': 'form-control', 'type': 'datetime-local'})
            elif isinstance(field.widget, forms.CheckboxInput):
                field.widget.attrs.update({'class': 'form-check-input'})
            elif isinstance(field.widget, forms.FileInput):
                field.widget.attrs.update({'class': 'form-control'})


# ===== نماذج المخازن =====

class StorageForm(BaseModelForm):
    """نموذج إضافة وتعديل المخازن"""
    
    class Meta:
        model = Storage
        fields = ['name', 'classification', 'keeper_name', 'phone_number', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'placeholder': 'اسم المخزن'}),
            'keeper_name': forms.TextInput(attrs={'placeholder': 'اسم أمين المخزن'}),
            'phone_number': forms.TextInput(attrs={'placeholder': '+966xxxxxxxxx'}),
        }
    
    def clean_name(self):
        name = self.cleaned_data.get('name')
        if name:
            # التحقق من عدم تكرار الاسم
            existing = Storage.objects.filter(name=name)
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            if existing.exists():
                raise ValidationError('يوجد مخزن بهذا الاسم مسبقاً')
        return name


# ===== نماذج الأصناف =====

class CategoryForm(BaseModelForm):
    """نموذج إضافة وتعديل الأصناف"""
    
    class Meta:
        model = Category
        fields = ['name', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'placeholder': 'اسم الصنف'}),
        }
    
    def clean_name(self):
        name = self.cleaned_data.get('name')
        if name:
            existing = Category.objects.filter(name=name)
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            if existing.exists():
                raise ValidationError('يوجد صنف بهذا الاسم مسبقاً')
        return name


# ===== نماذج الموردين =====

class SupplierForm(BaseModelForm):
    """نموذج إضافة وتعديل الموردين"""

    class Meta:
        model = Supplier
        fields = ['full_name', 'phone_number', 'is_active']
        widgets = {
            'full_name': forms.TextInput(attrs={'placeholder': 'الاسم الكامل للمورد'}),
            'phone_number': forms.TextInput(attrs={'placeholder': '+966xxxxxxxxx'}),
        }


# ===== نماذج المستفيدين =====

class BeneficiaryForm(BaseModelForm):
    """نموذج إضافة وتعديل المستفيدين"""

    class Meta:
        model = Beneficiary
        fields = ['full_name', 'phone_number', 'is_active']
        widgets = {
            'full_name': forms.TextInput(attrs={'placeholder': 'الاسم الكامل للمستفيد'}),
            'phone_number': forms.TextInput(attrs={'placeholder': '+966xxxxxxxxx'}),
        }


# ===== نماذج المحطات =====

class StationForm(BaseModelForm):
    """نموذج إضافة وتعديل المحطات"""

    class Meta:
        model = Station
        fields = ['name', 'address', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'placeholder': 'اسم المحطة'}),
            'address': forms.Textarea(attrs={'placeholder': 'عنوان المحطة', 'rows': 2}),
        }


# ===== نماذج عناصر المخازن =====

class StorageItemForm(BaseModelForm):
    """نموذج إضافة وتعديل عناصر المخازن"""
    
    class Meta:
        model = StorageItem
        fields = ['storage', 'category', 'unit_of_measure', 'opening_balance', 'current_quantity']
        widgets = {
            'opening_balance': forms.NumberInput(attrs={'step': '0.001', 'min': '0'}),
            'current_quantity': forms.NumberInput(attrs={'step': '0.001', 'min': '0'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # تصفية المخازن النشطة فقط
        self.fields['storage'].queryset = Storage.objects.filter(is_active=True)
        self.fields['category'].queryset = Category.objects.filter(is_active=True)


# ===== نماذج عمليات الوارد =====

class IncomingOperationForm(BaseModelForm):
    """نموذج إضافة وتعديل عمليات الوارد"""
    
    class Meta:
        model = IncomingOperation
        fields = [
            'paper_number', 'storage', 'supplier', 'station', 'operation_date',
            'supply_document_number', 'deliverer_name', 'receiver_name', 'statement'
        ]
        widgets = {
            'operation_date': forms.DateInput(attrs={'type': 'date'}),
            'paper_number': forms.TextInput(attrs={'placeholder': 'رقم الورقة'}),
            'supply_document_number': forms.TextInput(attrs={'placeholder': 'رقم مستند التوريد'}),
            'deliverer_name': forms.TextInput(attrs={'placeholder': 'اسم المسلم'}),
            'receiver_name': forms.TextInput(attrs={'placeholder': 'اسم المستلم'}),
            'statement': forms.Textarea(attrs={'placeholder': 'البيان', 'rows': 3}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['storage'].queryset = Storage.objects.filter(is_active=True)
        self.fields['supplier'].queryset = Supplier.objects.filter(is_active=True)
        self.fields['station'].queryset = Station.objects.filter(is_active=True)
        
        # تعيين التاريخ الحالي كقيمة افتراضية
        if not self.instance.pk:
            self.fields['operation_date'].initial = timezone.now().date()


class IncomingOperationItemForm(BaseModelForm):
    """نموذج عناصر عمليات الوارد"""

    class Meta:
        model = IncomingOperationItem
        fields = ['category', 'imported_quantity', 'notes']
        widgets = {
            'imported_quantity': forms.NumberInput(attrs={'step': '0.001', 'min': '0.001'}),
            'notes': forms.Textarea(attrs={'placeholder': 'ملاحظات', 'rows': 2}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['category'].queryset = Category.objects.filter(is_active=True)


# ===== نماذج عمليات الصادر =====

class OutgoingOperationForm(BaseModelForm):
    """نموذج إضافة وتعديل عمليات الصادر"""
    
    class Meta:
        model = OutgoingOperation
        fields = [
            'paper_number', 'storage', 'beneficiary', 'operation_date',
            'deliverer_name', 'receiver_name', 'statement'
        ]
        widgets = {
            'operation_date': forms.DateInput(attrs={'type': 'date'}),
            'paper_number': forms.TextInput(attrs={'placeholder': 'رقم الورقة'}),
            'deliverer_name': forms.TextInput(attrs={'placeholder': 'اسم المسلم'}),
            'receiver_name': forms.TextInput(attrs={'placeholder': 'اسم المستلم'}),
            'statement': forms.Textarea(attrs={'placeholder': 'البيان', 'rows': 3}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['storage'].queryset = Storage.objects.filter(is_active=True)
        self.fields['beneficiary'].queryset = Beneficiary.objects.filter(is_active=True)
        
        if not self.instance.pk:
            self.fields['operation_date'].initial = timezone.now().date()


class OutgoingOperationItemForm(BaseModelForm):
    """نموذج عناصر عمليات الصادر"""

    class Meta:
        model = OutgoingOperationItem
        fields = ['category', 'exported_quantity', 'transfer_date', 'actual_transfer_date', 'notes']
        widgets = {
            'exported_quantity': forms.NumberInput(attrs={'step': '0.001', 'min': '0.001'}),
            'transfer_date': forms.DateInput(attrs={'type': 'date'}),
            'actual_transfer_date': forms.DateInput(attrs={'type': 'date'}),
            'notes': forms.Textarea(attrs={'placeholder': 'ملاحظات', 'rows': 2}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['category'].queryset = Category.objects.filter(is_active=True)


# ===== نماذج تعديل العمليات =====

class OperationModificationForm(BaseModelForm):
    """نموذج تعديل العمليات"""
    
    class Meta:
        model = OperationModification
        fields = [
            'storage', 'operation_type', 'category', 'previous_quantity',
            'new_quantity', 'notes'
        ]
        widgets = {
            'previous_quantity': forms.NumberInput(attrs={'step': '0.001', 'readonly': True}),
            'new_quantity': forms.NumberInput(attrs={'step': '0.001', 'min': '0'}),
            'notes': forms.Textarea(attrs={'placeholder': 'ملاحظات التعديل', 'rows': 3}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['storage'].queryset = Storage.objects.filter(is_active=True)
        self.fields['category'].queryset = Category.objects.filter(is_active=True)


# ===== نماذج البحث والتصفية =====

class SearchForm(forms.Form):
    """نموذج البحث العام"""
    
    search_query = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'البحث...',
            'autocomplete': 'off'
        })
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )


class StorageFilterForm(forms.Form):
    """نموذج تصفية المخازن"""
    
    classification = forms.ChoiceField(
        choices=[('', 'جميع التصنيفات')] + Storage.CLASSIFICATION_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    is_active = forms.ChoiceField(
        choices=[('', 'الكل'), ('true', 'نشط'), ('false', 'غير نشط')],
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
