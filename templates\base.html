<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة مخازن المحروقات{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Cairo for better Arabic support -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Chart.js for data visualization -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    {% load static %}
    <link href="{% static 'css/custom.css' %}" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #64748b;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --info-color: #0891b2;
            --light-color: #f8fafc;
            --dark-color: #1e293b;
            --sidebar-bg: #1e293b;
            --sidebar-hover: #334155;
            --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --border-radius: 0.75rem;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
            line-height: 1.6;
        }

        .navbar-brand {
            font-weight: 600;
            font-size: 1.25rem;
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
            box-shadow: var(--card-shadow);
            border: none;
            padding: 1rem 0;
        }

        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            transition: var(--transition);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        .sidebar {
            min-height: calc(100vh - 76px);
            background: var(--sidebar-bg);
            border-radius: 0 var(--border-radius) var(--border-radius) 0;
            box-shadow: var(--card-shadow);
        }

        .sidebar .nav-link {
            color: #cbd5e1;
            padding: 0.75rem 1.25rem;
            margin: 0.25rem 0.5rem;
            border-radius: 0.5rem;
            transition: var(--transition);
            font-weight: 500;
        }

        .sidebar .nav-link:hover {
            color: #ffffff;
            background-color: var(--sidebar-hover);
            transform: translateX(-2px);
        }

        .sidebar .nav-link.active {
            color: #ffffff;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            box-shadow: 0 4px 6px -1px rgba(37, 99, 235, 0.3);
        }

        .sidebar .nav-link i {
            width: 20px;
            text-align: center;
        }

        .number-display {
            font-family: 'Courier New', monospace;
            text-align: left;
            direction: ltr;
            font-weight: 600;
        }

        .stat-card {
            transition: var(--transition);
            border: none;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .btn {
            border-radius: 0.5rem;
            font-weight: 500;
            transition: var(--transition);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            border: none;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 6px -1px rgba(37, 99, 235, 0.3);
        }

        .table {
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--card-shadow);
        }

        .table thead th {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            border: none;
            font-weight: 600;
        }

        .badge {
            border-radius: 0.5rem;
            font-weight: 500;
        }

        .alert {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
        }

        .main-content {
            padding: 2rem;
        }

        @media (max-width: 768px) {
            .sidebar {
                border-radius: 0;
            }

            .main-content {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'fuel_storage:dashboard' %}">
                <i class="fas fa-gas-pump me-2"></i>
                نظام إدارة مخازن المحروقات
            </a>
            <div class="navbar-nav ms-auto">
                {% if user.is_authenticated %}
                    <span class="navbar-text me-3">
                        مرحباً، {{ user.full_name|default:user.username }}
                    </span>
                    <a class="nav-link" href="/admin/">لوحة الإدارة</a>
                    <a class="nav-link" href="{% url 'admin:logout' %}">تسجيل الخروج</a>
                {% else %}
                    <a class="nav-link" href="{% url 'admin:login' %}">تسجيل الدخول</a>
                {% endif %}
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:dashboard' %}">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:storage_list' %}">
                                <i class="fas fa-warehouse me-2"></i>
                                المخازن
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:category_list' %}">
                                <i class="fas fa-tags me-2"></i>
                                الأصناف
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:supplier_list' %}">
                                <i class="fas fa-truck me-2"></i>
                                الموردون
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:incoming_operations_list' %}">
                                <i class="fas fa-arrow-down me-2"></i>
                                عمليات الوارد
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:storage_items_list' %}">
                                <i class="fas fa-boxes me-2"></i>
                                أصناف المخازن
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:beneficiary_list' %}">
                                <i class="fas fa-users me-2"></i>
                                المستفيدون
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:outgoing_operations_list' %}">
                                <i class="fas fa-arrow-up me-2"></i>
                                عمليات الصادر
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:operation_modifications_list' %}">
                                <i class="fas fa-edit me-2"></i>
                                تعديلات العمليات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:storage_report' %}">
                                <i class="fas fa-chart-bar me-2"></i>
                                تقارير المخازن
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:incoming_returns_list' %}">
                                <i class="fas fa-undo me-2"></i>
                                مرتجعات الوارد
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:outgoing_returns_list' %}">
                                <i class="fas fa-redo me-2"></i>
                                مرتجعات الصادر
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:returns_summary' %}">
                                <i class="fas fa-clipboard-list me-2"></i>
                                ملخص المرتجعات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:damage_operations_list' %}">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                عمليات التلف
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:storage_transfers_list' %}">
                                <i class="fas fa-exchange-alt me-2"></i>
                                التحويل المخزني
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:operations_summary' %}">
                                <i class="fas fa-chart-pie me-2"></i>
                                ملخص العمليات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'fuel_storage:reports_dashboard' %}">
                                <i class="fas fa-file-alt me-2"></i>
                                التقارير المتقدمة
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 main-content">
                <div class="content-wrapper">
                    {% block content %}
                    {% endblock %}
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script src="{% static 'js/custom.js' %}"></script>

    <!-- Custom JavaScript -->
    <script>
        // Add active class to current page in sidebar
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.sidebar .nav-link');

            navLinks.forEach(link => {
                if (link.getAttribute('href') === currentPath) {
                    link.classList.add('active');
                }
            });
        });

        // Add loading animation to buttons
        function addLoadingToButton(button) {
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحميل...';
            button.disabled = true;

            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;
            }, 2000);
        }

        // Add smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>
